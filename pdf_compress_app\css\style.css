/**
 * PDF Compression Application Styles
 * 
 * Custom CSS for the standalone PDF compression application
 * Designed to match ResourceSpace's look and feel
 */

/* Application Navigation */
.pdf-compress-nav {
    margin: 20px 0;
}

.nav-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.nav-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    min-width: 150px;
    text-align: center;
}

.nav-button:hover {
    background: #e9ecef;
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.nav-button.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.nav-button i {
    margin-bottom: 10px;
}

.nav-button span {
    font-weight: 500;
    font-size: 14px;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
}

.status-indicator.status-success i {
    color: #28a745;
}

.status-indicator.status-error i {
    color: #dc3545;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.status-pending {
    background: #ffc107;
    color: #212529;
}

.status-badge.status-processing {
    background: #17a2b8;
    color: white;
}

.status-badge.status-completed {
    background: #28a745;
    color: white;
}

.status-badge.status-failed {
    background: #dc3545;
    color: white;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area.drag-over {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.upload-content i {
    color: #6c757d;
    margin-bottom: 10px;
}

.upload-content p {
    color: #6c757d;
    margin: 0;
    font-size: 16px;
}

/* File Info Display */
.file-info {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.file-preview {
    flex-shrink: 0;
}

.file-preview i {
    color: #dc3545;
    font-size: 3em;
}

.file-details {
    flex-grow: 1;
}

.file-details h3 {
    margin: 0 0 10px 0;
    color: #495057;
}

.file-details p {
    margin: 5px 0;
    color: #6c757d;
}

.file-actions {
    flex-shrink: 0;
}

/* Slider Controls */
.slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
}

.quality-slider,
.scale-slider {
    flex-grow: 1;
    height: 6px;
    border-radius: 3px;
    background: #dee2e6;
    outline: none;
    -webkit-appearance: none;
}

.quality-slider::-webkit-slider-thumb,
.scale-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
}

.quality-slider::-moz-range-thumb,
.scale-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
}

.slider-value {
    min-width: 40px;
    text-align: center;
    font-weight: bold;
    color: #007bff;
}

/* Advanced Options */
.advanced-options {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
    margin-top: 20px;
}

.advanced-toggle {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.advanced-toggle:hover {
    text-decoration: underline;
}

/* Progress Bar */
.progress-container {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-text {
    color: #495057;
    font-weight: 500;
}

.progress-percentage {
    color: #007bff;
    font-weight: bold;
    font-size: 18px;
}

.progress-details {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    margin-top: 10px;
}

.progress-actions {
    text-align: center;
    margin-top: 20px;
}

/* Results Grid */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.result-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.result-icon {
    margin-right: 15px;
    flex-shrink: 0;
}

.result-icon i {
    font-size: 2em;
    color: #007bff;
}

.result-content {
    flex-grow: 1;
}

.result-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}

.result-value {
    font-size: 18px;
    font-weight: bold;
    color: #495057;
}

.result-actions {
    text-align: center;
    margin-top: 30px;
}

.result-actions .btn {
    margin: 0 10px;
}

/* Quick Start Steps */
.quick-start-steps {
    display: flex;
    gap: 30px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.step {
    flex: 1;
    min-width: 250px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    flex-shrink: 0;
}

.step-content h3 {
    margin: 0 0 10px 0;
    color: #495057;
}

.step-content p {
    margin: 0;
    color: #6c757d;
    line-height: 1.5;
}

/* Recent Activity */
.recent-activity {
    margin: 20px 0;
}

.no-activity {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.no-activity i {
    font-size: 3em;
    margin-bottom: 20px;
    color: #dee2e6;
}

.no-activity p {
    margin-bottom: 20px;
    font-size: 16px;
}

/* History Table */
.InfoTable {
    width: 100%;
    margin: 20px 0;
}

.InfoTable th,
.InfoTable td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.InfoTable th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.InfoTable tr:hover {
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-button {
        width: 100%;
        max-width: 300px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .file-info {
        flex-direction: column;
        text-align: center;
    }
    
    .quick-start-steps {
        flex-direction: column;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .slider-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .progress-info {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

/* Alert Messages */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit;
    background: none;
    border: none;
    font-size: 21px;
    font-weight: bold;
    line-height: 1;
    cursor: pointer;
}

/* Button Enhancements */
.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 5px; }
.mb-2 { margin-bottom: 10px; }
.mb-3 { margin-bottom: 15px; }
.mb-4 { margin-bottom: 20px; }
.mb-5 { margin-bottom: 25px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 5px; }
.mt-2 { margin-top: 10px; }
.mt-3 { margin-top: 15px; }
.mt-4 { margin-top: 20px; }
.mt-5 { margin-top: 25px; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
