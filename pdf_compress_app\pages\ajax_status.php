<?php
/**
 * PDF Compression Application - AJAX Status Handler
 * 
 * Checks compression job status from the external API
 */

include '../../../include/db.php';
include_once '../../../include/general.php';
include_once '../../../include/authenticate.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($username)) {
    echo json_encode([
        'success' => false,
        'message' => $lang['error-permissiondenied']
    ]);
    exit();
}

// Get job ID
$job_id = getvalescaped('job_id', '');

if (empty($job_id)) {
    echo json_encode([
        'success' => false,
        'message' => 'Job ID is required'
    ]);
    exit();
}

// Check if job belongs to current user (from session)
if (!isset($_SESSION['pdf_compress_jobs'][$job_id])) {
    echo json_encode([
        'success' => false,
        'message' => 'Job not found or access denied'
    ]);
    exit();
}

$job_data = $_SESSION['pdf_compress_jobs'][$job_id];

try {
    // Prepare API request
    $api_url = rtrim($pdf_compress_api_url, '/') . '/status/' . urlencode($job_id);
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $pdf_compress_status_check_timeout,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $pdf_compress_api_key,
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false, // For development - should be true in production
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);
    
    // Execute request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    // Check for cURL errors
    if ($curl_error) {
        echo json_encode([
            'success' => false,
            'message' => $lang['pdf_compress_error_network']
        ]);
        exit();
    }
    
    // Check HTTP response code
    if ($http_code === 404) {
        echo json_encode([
            'success' => false,
            'message' => 'Job not found'
        ]);
        exit();
    }
    
    if ($http_code !== 200) {
        echo json_encode([
            'success' => false,
            'message' => $lang['pdf_compress_error_api_unavailable']
        ]);
        exit();
    }
    
    // Parse JSON response
    $api_response = json_decode($response, true);
    
    if (!$api_response) {
        echo json_encode([
            'success' => false,
            'message' => $lang['pdf_compress_error_invalid_response']
        ]);
        exit();
    }
    
    // Update job data in session
    $job_data['status'] = $api_response['status'];
    $job_data['updated_date'] = date('Y-m-d H:i:s');
    
    if (isset($api_response['progress'])) {
        $job_data['progress'] = $api_response['progress'];
    }
    
    if (isset($api_response['current_page'])) {
        $job_data['current_page'] = $api_response['current_page'];
    }
    
    if (isset($api_response['total_pages'])) {
        $job_data['total_pages'] = $api_response['total_pages'];
    }
    
    if (isset($api_response['message'])) {
        $job_data['message'] = $api_response['message'];
    }
    
    // Handle completion
    if ($api_response['status'] === 'completed') {
        if (isset($api_response['download_url'])) {
            $job_data['download_url'] = $api_response['download_url'];
        }
        
        if (isset($api_response['compressed_size_mb'])) {
            $job_data['compressed_size'] = $api_response['compressed_size_mb'] * 1024 * 1024;
        }
        
        if (isset($api_response['compression_ratio'])) {
            $job_data['compression_ratio'] = $api_response['compression_ratio'];
        }
        
        if (isset($api_response['original_size_mb'])) {
            $job_data['original_size_confirmed'] = $api_response['original_size_mb'] * 1024 * 1024;
        }
        
        // Calculate size reduction
        if (isset($job_data['compressed_size']) && isset($job_data['original_size'])) {
            $reduction = (($job_data['original_size'] - $job_data['compressed_size']) / $job_data['original_size']) * 100;
            $job_data['size_reduction'] = $reduction;
        }
        
        pdf_compress_log_info("Compression completed for user $username, job ID: $job_id");
    }
    
    // Handle failure
    if ($api_response['status'] === 'failed') {
        if (isset($api_response['error_details'])) {
            $job_data['error_details'] = $api_response['error_details'];
        }
        
        pdf_compress_log_error("Compression failed for user $username, job ID: $job_id - " . 
                              ($api_response['error_details'] ?? 'Unknown error'));
    }
    
    // Update session data
    $_SESSION['pdf_compress_jobs'][$job_id] = $job_data;
    
    // Prepare response data
    $response_data = [
        'job_id' => $job_id,
        'status' => $api_response['status'],
        'progress' => $api_response['progress'] ?? 0,
        'message' => $api_response['message'] ?? '',
        'current_page' => $api_response['current_page'] ?? null,
        'total_pages' => $api_response['total_pages'] ?? null,
        'original_size_mb' => $api_response['original_size_mb'] ?? null,
        'compressed_size_mb' => $api_response['compressed_size_mb'] ?? null,
        'compression_ratio' => $api_response['compression_ratio'] ?? null,
        'download_url' => $api_response['download_url'] ?? null,
        'error_details' => $api_response['error_details'] ?? null
    ];
    
    // Return success response
    echo json_encode([
        'success' => true,
        'data' => $response_data
    ]);
    
} catch (Exception $e) {
    pdf_compress_log_error("Exception in status check: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_server']
    ]);
}

/**
 * Helper function to log errors
 */
function pdf_compress_log_error($message) {
    global $pdf_compress_enable_logging, $pdf_compress_log_file;
    
    if ($pdf_compress_enable_logging) {
        $log_entry = date('Y-m-d H:i:s') . " [ERROR] " . $message . "\n";
        error_log($log_entry, 3, $pdf_compress_log_file);
    }
}

/**
 * Helper function to log info
 */
function pdf_compress_log_info($message) {
    global $pdf_compress_enable_logging, $pdf_compress_log_file;
    
    if ($pdf_compress_enable_logging) {
        $log_entry = date('Y-m-d H:i:s') . " [INFO] " . $message . "\n";
        error_log($log_entry, 3, $pdf_compress_log_file);
    }
}
?>
