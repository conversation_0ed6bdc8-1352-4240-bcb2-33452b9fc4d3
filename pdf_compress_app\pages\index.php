<?php
/**
 * PDF Compression Application - Main Index Page
 * 
 * Standalone PDF compression application main page
 */

include '../../../include/db.php';
include_once '../../../include/general.php';
include_once '../../../include/authenticate.php';

// Check if user is logged in
if (!isset($username)) {
    redirect('pages/login.php?url=' . urlencode($_SERVER['REQUEST_URI']));
    exit();
}

// Check user group permissions
if (!empty($pdf_compress_allowed_user_groups) && 
    !in_array($usergroup, $pdf_compress_allowed_user_groups)) {
    exit($lang['pdf_compress_error_permission_denied']);
}

// Include header
include '../../../include/header.php';
?>

<div class="BasicsBox">
    <h1>
        <i class="fa fa-compress fa-fw"></i>
        <?php echo $lang['pdf_compress_app_title']; ?>
    </h1>
    <p><?php echo $lang['pdf_compress_app_description']; ?></p>
</div>

<!-- Messages area -->
<div id="messages"></div>

<!-- Main Application Navigation -->
<div class="BasicsBox">
    <div class="pdf-compress-nav">
        <div class="nav-buttons">
            <a href="upload.php" class="nav-button active" onclick="return CentralSpaceLoad(this, true);">
                <i class="fa fa-upload fa-2x"></i>
                <span><?php echo $lang['pdf_compress_nav_upload']; ?></span>
            </a>
            
            <a href="history.php" class="nav-button" onclick="return CentralSpaceLoad(this, true);">
                <i class="fa fa-history fa-2x"></i>
                <span><?php echo $lang['pdf_compress_nav_history']; ?></span>
            </a>
            
            <?php if (checkperm('a')): ?>
            <a href="setup.php" class="nav-button" onclick="return CentralSpaceLoad(this, true);">
                <i class="fa fa-cog fa-2x"></i>
                <span><?php echo $lang['pdf_compress_nav_settings']; ?></span>
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="BasicsBox">
    <h2><?php echo $lang['statistics']; ?></h2>
    
    <div class="stats-grid">
        <div class="stat-item">
            <div class="stat-number" id="total-compressions">
                <?php echo pdf_compress_get_user_compression_count($username); ?>
            </div>
            <div class="stat-label"><?php echo $lang['pdf_compress_total_compressions']; ?></div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number" id="total-savings">
                <?php echo pdf_compress_get_user_total_savings($username); ?>
            </div>
            <div class="stat-label"><?php echo $lang['pdf_compress_total_savings']; ?></div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number" id="avg-reduction">
                <?php echo pdf_compress_get_user_avg_reduction($username); ?>%
            </div>
            <div class="stat-label"><?php echo $lang['pdf_compress_avg_reduction']; ?></div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number" id="service-status">
                <span class="status-indicator" id="api-status">
                    <i class="fa fa-circle"></i>
                </span>
            </div>
            <div class="stat-label"><?php echo $lang['pdf_compress_service_status']; ?></div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_recent_activity']; ?></h2>
    
    <div class="recent-activity">
        <?php
        $recent_jobs = pdf_compress_get_recent_jobs($username, 5);
        
        if (empty($recent_jobs)):
        ?>
            <div class="no-activity">
                <i class="fa fa-info-circle"></i>
                <p><?php echo $lang['pdf_compress_no_recent_activity']; ?></p>
                <a href="upload.php" class="btn btn-primary" onclick="return CentralSpaceLoad(this, true);">
                    <?php echo $lang['pdf_compress_start_first_compression']; ?>
                </a>
            </div>
        <?php else: ?>
            <table class="InfoTable">
                <thead>
                    <tr>
                        <th><?php echo $lang['pdf_compress_filename']; ?></th>
                        <th><?php echo $lang['pdf_compress_history_date']; ?></th>
                        <th><?php echo $lang['pdf_compress_history_status']; ?></th>
                        <th><?php echo $lang['pdf_compress_size_reduction']; ?></th>
                        <th><?php echo $lang['actions']; ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_jobs as $job): ?>
                    <tr>
                        <td>
                            <i class="fa fa-file-pdf-o"></i>
                            <?php echo htmlspecialchars($job['filename']); ?>
                        </td>
                        <td><?php echo nicedate($job['created_date'], true); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $job['status']; ?>">
                                <?php echo $lang['pdf_compress_status_' . $job['status']]; ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($job['status'] === 'completed' && isset($job['size_reduction'])): ?>
                                <?php echo round($job['size_reduction'], 1); ?>%
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($job['status'] === 'completed' && isset($job['download_url'])): ?>
                                <a href="<?php echo $job['download_url']; ?>" class="btn btn-sm">
                                    <i class="fa fa-download"></i> <?php echo $lang['download']; ?>
                                </a>
                            <?php elseif ($job['status'] === 'failed'): ?>
                                <a href="upload.php" class="btn btn-sm" onclick="return CentralSpaceLoad(this, true);">
                                    <i class="fa fa-refresh"></i> <?php echo $lang['pdf_compress_retry']; ?>
                                </a>
                            <?php elseif ($job['status'] === 'processing'): ?>
                                <a href="status.php?job_id=<?php echo $job['job_id']; ?>" class="btn btn-sm" onclick="return CentralSpaceLoad(this, true);">
                                    <i class="fa fa-eye"></i> <?php echo $lang['view']; ?>
                                </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="text-center" style="margin-top: 15px;">
                <a href="history.php" class="btn" onclick="return CentralSpaceLoad(this, true);">
                    <?php echo $lang['pdf_compress_view_all_history']; ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Start Guide -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_quick_start']; ?></h2>
    
    <div class="quick-start-steps">
        <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
                <h3><?php echo $lang['pdf_compress_step_upload']; ?></h3>
                <p><?php echo $lang['pdf_compress_step_upload_desc']; ?></p>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
                <h3><?php echo $lang['pdf_compress_step_configure']; ?></h3>
                <p><?php echo $lang['pdf_compress_step_configure_desc']; ?></p>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
                <h3><?php echo $lang['pdf_compress_step_download']; ?></h3>
                <p><?php echo $lang['pdf_compress_step_download_desc']; ?></p>
            </div>
        </div>
    </div>
    
    <div class="text-center" style="margin-top: 20px;">
        <a href="upload.php" class="btn btn-primary btn-lg" onclick="return CentralSpaceLoad(this, true);">
            <i class="fa fa-upload"></i> <?php echo $lang['pdf_compress_get_started']; ?>
        </a>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Check API status
    checkApiStatus();
    
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        refreshStats();
    }, 30000);
    
    function checkApiStatus() {
        $.ajax({
            url: 'ajax_health_check.php',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.status === 'healthy') {
                    $('#api-status').removeClass('status-error').addClass('status-success');
                    $('#api-status i').removeClass('fa-times-circle').addClass('fa-check-circle');
                } else {
                    $('#api-status').removeClass('status-success').addClass('status-error');
                    $('#api-status i').removeClass('fa-check-circle').addClass('fa-times-circle');
                }
            },
            error: function() {
                $('#api-status').removeClass('status-success').addClass('status-error');
                $('#api-status i').removeClass('fa-check-circle').addClass('fa-times-circle');
            }
        });
    }
    
    function refreshStats() {
        $.ajax({
            url: 'ajax_stats.php',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#total-compressions').text(response.data.total_compressions);
                    $('#total-savings').text(response.data.total_savings);
                    $('#avg-reduction').text(response.data.avg_reduction + '%');
                }
            }
        });
    }
});
</script>

<?php
/**
 * Helper functions for the main page
 */

function pdf_compress_get_user_compression_count($username) {
    // This would query a database table to get user's compression count
    // For now, return a placeholder
    return 0;
}

function pdf_compress_get_user_total_savings($username) {
    // This would calculate total file size savings
    // For now, return a placeholder
    return '0 MB';
}

function pdf_compress_get_user_avg_reduction($username) {
    // This would calculate average compression ratio
    // For now, return a placeholder
    return 0;
}

function pdf_compress_get_recent_jobs($username, $limit = 5) {
    // This would query recent compression jobs
    // For now, return empty array
    return array();
}

// Add missing language strings
$lang['pdf_compress_total_compressions'] = 'Total Compressions';
$lang['pdf_compress_total_savings'] = 'Total Savings';
$lang['pdf_compress_avg_reduction'] = 'Average Reduction';
$lang['pdf_compress_service_status'] = 'Service Status';
$lang['pdf_compress_recent_activity'] = 'Recent Activity';
$lang['pdf_compress_no_recent_activity'] = 'No recent compression activity';
$lang['pdf_compress_start_first_compression'] = 'Start Your First Compression';
$lang['pdf_compress_view_all_history'] = 'View All History';
$lang['pdf_compress_quick_start'] = 'Quick Start Guide';
$lang['pdf_compress_step_upload'] = 'Upload PDF';
$lang['pdf_compress_step_upload_desc'] = 'Select and upload your PDF file';
$lang['pdf_compress_step_configure'] = 'Configure Options';
$lang['pdf_compress_step_configure_desc'] = 'Choose compression settings';
$lang['pdf_compress_step_download'] = 'Download Result';
$lang['pdf_compress_step_download_desc'] = 'Download your compressed file';
$lang['pdf_compress_get_started'] = 'Get Started';

include '../../../include/footer.php';
?>
