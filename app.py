import os
import fitz  # PyMuPDF
from PIL import Image
import io

def compress_pdf_improved(input_path, output_path, image_quality=75, image_scale=0.8, skip_pages=None):
    """
    Compress PDF by reducing image quality while preserving the document structure
    
    Args:
        input_path: Path to input PDF
        output_path: Path to output PDF
        image_quality: JPEG quality (1-100)
        image_scale: Scale factor for large images
        skip_pages: List of page numbers to skip (1-based indexing)
    """
    
    if skip_pages is None:
        skip_pages = []
    
    # Open the PDF
    pdf_document = fitz.open(input_path)
    
    # Get initial file size
    initial_size = os.path.getsize(input_path) / (1024 * 1024)
    print(f"Initial file size: {initial_size:.2f} MB")
    
    # Process each page
    for page_num in range(len(pdf_document)):
        page_display_num = page_num + 1  # Human-readable page number
        
        # Check if this page should be skipped
        if page_display_num in skip_pages:
            print(f"Skipping page {page_display_num}/{len(pdf_document)} (excluded from compression)")
            continue
            
        print(f"Processing page {page_display_num}/{len(pdf_document)}...", end='\r')
        
        page = pdf_document[page_num]
        
        # Get list of images on this page
        image_list = page.get_images()
        
        # Process each image
        for img_index, img in enumerate(image_list):
            try:
                # Extract image
                xref = img[0]
                
                # Check if image exists and can be extracted
                if xref == 0:
                    continue
                    
                pix = fitz.Pixmap(pdf_document, xref)
                
                # Skip if pixmap is invalid
                if pix.width <= 0 or pix.height <= 0:
                    pix = None
                    continue
                
                # Convert to PIL Image
                img_data = pix.tobytes()
                img_pil = Image.open(io.BytesIO(img_data))
                
                # Convert RGBA to RGB if necessary
                if img_pil.mode == "RGBA":
                    rgb_img = Image.new("RGB", img_pil.size, (255, 255, 255))
                    rgb_img.paste(img_pil, mask=img_pil.split()[3])
                    img_pil = rgb_img
                elif img_pil.mode not in ["RGB", "L"]:
                    img_pil = img_pil.convert("RGB")
                
                # Scale down large images
                if img_pil.width > 1500 or img_pil.height > 1500:
                    new_width = int(img_pil.width * image_scale)
                    new_height = int(img_pil.height * image_scale)
                    if new_width > 0 and new_height > 0:
                        img_pil = img_pil.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Compress image
                img_buffer = io.BytesIO()
                img_pil.save(img_buffer, format='JPEG', quality=image_quality, optimize=True)
                img_data = img_buffer.getvalue()
                
                # Replace image in PDF
                pdf_document.xref_set_key(xref, "Filter", "[/DCTDecode]")
                pdf_document.xref_stream(xref, img_data)
                
                # Clean up
                pix = None
                img_buffer.close()
                
            except Exception as e:
                # Skip problematic images
                continue
    
    # Save with garbage collection and compression
    pdf_document.save(output_path, garbage=4, deflate=True, clean=True)
    pdf_document.close()
    
    # Check final size
    final_size = os.path.getsize(output_path) / (1024 * 1024)
    print(f"\nFinal file size: {final_size:.2f} MB")
    print(f"Compression ratio: {(1 - final_size/initial_size) * 100:.1f}%")
    
    return final_size

def ghostscript_compress(input_path, output_path, quality_setting="/ebook"):
    """
    Use Ghostscript for reliable PDF compression
    """
    import subprocess
    import sys
    
    # Determine Ghostscript executable based on OS
    if sys.platform == "win32":
        gs_cmd = "gswin64c"  # or "gswin32c" for 32-bit
    else:
        gs_cmd = "gs"
    
    gs_command = [
        gs_cmd,
        "-sDEVICE=pdfwrite",
        "-dCompatibilityLevel=1.4",
        f"-dPDFSETTINGS={quality_setting}",
        "-dNOPAUSE",
        "-dBATCH",
        "-dQUIET",
        "-dDownsampleColorImages=true",
        "-dDownsampleGrayImages=true",
        "-dColorImageResolution=150",
        "-dGrayImageResolution=150",
        "-dColorImageDownsampleType=/Bicubic",
        "-dGrayImageDownsampleType=/Bicubic",
        "-dColorConversionStrategy=/RGB",
        "-dAutoRotatePages=/None",
        "-dEmbedAllFonts=true",
        "-dSubsetFonts=true",
        f"-sOutputFile={output_path}",
        input_path
    ]
    
    try:
        print("Running Ghostscript compression...")
        subprocess.run(gs_command, check=True)
        
        # Check file sizes
        initial_size = os.path.getsize(input_path) / (1024 * 1024)
        final_size = os.path.getsize(output_path) / (1024 * 1024)
        print(f"Initial size: {initial_size:.2f} MB")
        print(f"Final size: {final_size:.2f} MB")
        print(f"Compression ratio: {(1 - final_size/initial_size) * 100:.1f}%")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Ghostscript compression failed: {e}")
        return False
    except FileNotFoundError:
        print("Ghostscript not found. Please install Ghostscript:")
        print("Windows: https://www.ghostscript.com/download/gsdnld.html")
        print("Mac: brew install ghostscript")
        print("Linux: sudo apt-get install ghostscript")
        return False

def compress_with_pdf2image(input_path, output_path, dpi=150, quality=85, skip_pages=None):
    """
    Alternative method: Convert PDF to images and back
    Requires: pip install pdf2image
    """
    if skip_pages is None:
        skip_pages = []
        
    try:
        from pdf2image import convert_from_path
        import img2pdf
        
        print("Converting PDF to images...")
        
        # Convert PDF pages to images
        images = convert_from_path(input_path, dpi=dpi)
        
        # Compress each image
        compressed_images = []
        for i, img in enumerate(images):
            page_num = i + 1
            
            if page_num in skip_pages:
                print(f"Keeping original quality for page {page_num}/{len(images)}...", end='\r')
                # Keep original quality for skipped pages
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='JPEG', quality=95, optimize=False)
                compressed_images.append(img_buffer.getvalue())
            else:
                print(f"Compressing page {page_num}/{len(images)}...", end='\r')
                
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Save to bytes with compression
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='JPEG', quality=quality, optimize=True)
                compressed_images.append(img_buffer.getvalue())
        
        print("\nRebuilding PDF...")
        
        # Convert back to PDF
        with open(output_path, "wb") as f:
            f.write(img2pdf.convert(compressed_images))
        
        # Check results
        initial_size = os.path.getsize(input_path) / (1024 * 1024)
        final_size = os.path.getsize(output_path) / (1024 * 1024)
        print(f"Initial size: {initial_size:.2f} MB")
        print(f"Final size: {final_size:.2f} MB")
        print(f"Compression ratio: {(1 - final_size/initial_size) * 100:.1f}%")
        
        return True
        
    except ImportError:
        print("pdf2image not installed. Install with: pip install pdf2image img2pdf")
        return False
    except Exception as e:
        print(f"Error in pdf2image method: {e}")
        return False

def main():
    # File paths
    input_file = r"C:\Users\<USER>\Apps\pdfcompress\input_2.pdf"
    output_file = r"C:\Users\<USER>\Apps\pdfcompress\compressed_output.pdf"
    
    # Pages to skip from compression (1-based indexing)
    skip_pages = [1, 10, 11]  # Skip pages 10 and 11
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file not found: {input_file}")
        return
    
    print(f"Starting PDF compression...")
    print(f"Input: {input_file}")
    print(f"Target: ~40 MB from 70 MB")
    print(f"Skipping pages: {skip_pages}")
    print("-" * 50)
    
    # Method 1: Try improved PyMuPDF compression
    print("\nMethod 1: PyMuPDF compression")
    try:
        quality = 75
        while quality >= 30:
            temp_output = output_file.replace('.pdf', f'_temp_{quality}.pdf')
            print(f"\nTrying quality={quality}")
            
            final_size = compress_pdf_improved(input_file, temp_output, 
                                             image_quality=quality, 
                                             image_scale=0.8,
                                             skip_pages=skip_pages)
            
            if final_size <= 42:  # Allow 2MB margin
                os.rename(temp_output, output_file)
                print(f"\nSuccess! Compressed to {final_size:.2f} MB")
                return
            else:
                os.remove(temp_output)
                quality -= 10
                
    except Exception as e:
        print(f"PyMuPDF method error: {e}")
    
    # Method 2: Try Ghostscript
    print("\n" + "-" * 50)
    print("Method 2: Ghostscript compression")
    print("Note: Ghostscript will compress all pages")
    
    temp_output = output_file.replace('.pdf', '_gs_temp.pdf')
    if ghostscript_compress(input_file, temp_output, "/ebook"):
        # Check if size is acceptable
        size = os.path.getsize(temp_output) / (1024 * 1024)
        if size <= 45:
            os.rename(temp_output, output_file)
            print("\nSuccess with Ghostscript!")
            return
        else:
            # Try with more aggressive compression
            print("\nTrying more aggressive compression...")
            os.remove(temp_output)
            if ghostscript_compress(input_file, output_file, "/screen"):
                print("\nSuccess with aggressive compression!")
                return
    
    # Method 3: Try pdf2image method
    print("\n" + "-" * 50)
    print("Method 3: PDF to Image conversion")
    
    if compress_with_pdf2image(input_file, output_file, dpi=150, quality=80, skip_pages=skip_pages):
        print("\nSuccess with pdf2image method!")
        return
    
    print("\n" + "-" * 50)
    print("All methods attempted. Check output for best result.")

if __name__ == "__main__":
    main()