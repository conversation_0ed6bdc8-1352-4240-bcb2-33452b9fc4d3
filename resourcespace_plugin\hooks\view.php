<?php
/**
 * PDF Compress Plugin - View Page Hooks
 * 
 * Adds PDF compression tool to resource view page
 */

/**
 * Add PDF compression tool to resource tools section
 */
function HookPdf_compressViewRenderresourcetools()
{
    global $resource, $lang, $baseurl, $pdf_compress_allowed_resource_types, 
           $pdf_compress_allowed_user_groups, $usergroup;
    
    // Check if this is a PDF resource
    if (!isset($resource['file_extension']) || strtolower($resource['file_extension']) !== 'pdf') {
        return false;
    }
    
    // Check resource type restrictions
    if (!empty($pdf_compress_allowed_resource_types) && 
        !in_array($resource['resource_type'], $pdf_compress_allowed_resource_types)) {
        return false;
    }
    
    // Check user group restrictions
    if (!empty($pdf_compress_allowed_user_groups) && 
        !in_array($usergroup, $pdf_compress_allowed_user_groups)) {
        return false;
    }
    
    // Check if user has edit access to the resource
    if (!get_edit_access($resource['ref'])) {
        return false;
    }
    
    // Check if the resource file exists
    $file_path = get_resource_path($resource['ref'], true, '', false, $resource['file_extension']);
    if (!file_exists($file_path)) {
        return false;
    }
    
    // Get file size
    $file_size_bytes = filesize($file_path);
    $file_size_mb = round($file_size_bytes / (1024 * 1024), 2);
    
    // Check file size limit
    global $pdf_compress_max_file_size_mb;
    if ($file_size_mb > $pdf_compress_max_file_size_mb) {
        return false;
    }
    
    // Render the compression tool
    ?>
    <li>
        <a href="<?php echo $baseurl; ?>/plugins/pdf_compress/pages/compress.php?ref=<?php echo $resource['ref']; ?>" 
           onclick="return CentralSpaceLoad(this, true);">
            <i class="fa fa-compress" aria-hidden="true"></i>&nbsp;
            <?php echo $lang['pdf_compress_tool_title']; ?>
        </a>
    </li>
    <?php
    
    return true;
}

/**
 * Add PDF compression JavaScript and CSS to the view page
 */
function HookPdf_compressViewAdditionalheaderhtml()
{
    global $baseurl;
    ?>
    <link rel="stylesheet" type="text/css" href="<?php echo $baseurl; ?>/plugins/pdf_compress/css/style.css">
    <script type="text/javascript">
        // PDF Compression utility functions
        var pdfCompress = {
            baseUrl: '<?php echo $baseurl; ?>',
            
            // Show compression modal
            showModal: function(resourceRef) {
                var url = this.baseUrl + '/plugins/pdf_compress/pages/compress.php?ref=' + resourceRef;
                CentralSpaceLoad(url, true);
            },
            
            // Check if compression is available for this resource
            isAvailable: function(fileExtension, fileSize) {
                return fileExtension.toLowerCase() === 'pdf' && fileSize <= <?php echo $pdf_compress_max_file_size_mb; ?>;
            }
        };
    </script>
    <?php
}

/**
 * Add compression status indicator to resource metadata
 */
function HookPdf_compressViewRenderresourcemetadata()
{
    global $resource, $lang, $baseurl;
    
    // Only show for PDF resources
    if (!isset($resource['file_extension']) || strtolower($resource['file_extension']) !== 'pdf') {
        return false;
    }
    
    // Check if there are any compression jobs for this resource
    $compression_jobs = pdf_compress_get_resource_jobs($resource['ref']);
    
    if (!empty($compression_jobs)) {
        ?>
        <div class="pdf-compress-status-indicator">
            <h3><?php echo $lang['pdf_compress_plugin_name']; ?></h3>
            <?php foreach ($compression_jobs as $job): ?>
                <div class="compression-job-status status-<?php echo $job['status']; ?>">
                    <span class="status-icon"></span>
                    <span class="status-text">
                        <?php echo $lang['pdf_compress_status_' . $job['status']]; ?>
                        <?php if ($job['status'] === 'processing' && isset($job['progress'])): ?>
                            (<?php echo round($job['progress']); ?>%)
                        <?php endif; ?>
                    </span>
                    <?php if ($job['status'] === 'completed' && isset($job['download_url'])): ?>
                        <a href="<?php echo $job['download_url']; ?>" class="download-link">
                            <?php echo $lang['pdf_compress_download']; ?>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    return true;
}

/**
 * Helper function to get compression jobs for a resource
 */
function pdf_compress_get_resource_jobs($resource_ref)
{
    // This would typically query a database table to get compression job status
    // For now, we'll return an empty array as this is a basic implementation
    // In a full implementation, you would:
    // 1. Create a database table to track compression jobs
    // 2. Store job IDs and status information
    // 3. Query this table to get current job status
    
    return array();
}

/**
 * Add compression information to resource log
 */
function HookPdf_compressViewResourcelog()
{
    global $resource, $lang;
    
    // Get compression history for this resource
    $compression_history = pdf_compress_get_compression_history($resource['ref']);
    
    if (!empty($compression_history)) {
        foreach ($compression_history as $entry) {
            ?>
            <tr>
                <td><?php echo nicedate($entry['date'], true); ?></td>
                <td><?php echo $entry['user']; ?></td>
                <td><?php echo $lang['pdf_compress_log_' . $entry['action']]; ?></td>
                <td><?php echo $entry['details']; ?></td>
            </tr>
            <?php
        }
    }
}

/**
 * Helper function to get compression history for a resource
 */
function pdf_compress_get_compression_history($resource_ref)
{
    // This would query the resource log or a dedicated compression log table
    // For now, return empty array
    return array();
}
