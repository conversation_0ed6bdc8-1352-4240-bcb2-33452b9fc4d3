<?php
/**
 * PDF Compression Application - Global Hooks
 * 
 * Adds navigation menu item for the standalone application
 */

/**
 * Add PDF Compression to the main navigation menu
 */
function HookPdf_compressAllAddmenuitem()
{
    global $lang, $baseurl, $pdf_compress_show_in_main_menu, 
           $pdf_compress_allowed_user_groups, $usergroup, $username;
    
    // Check if menu item should be shown
    if (!$pdf_compress_show_in_main_menu) {
        return false;
    }
    
    // Check if user is logged in
    if (!isset($username)) {
        return false;
    }
    
    // Check user group restrictions
    if (!empty($pdf_compress_allowed_user_groups) && 
        !in_array($usergroup, $pdf_compress_allowed_user_groups)) {
        return false;
    }
    
    // Add menu item
    ?>
    <li>
        <a href="<?php echo $baseurl; ?>/plugins/pdf_compress/pages/index.php" 
           onclick="return CentralSpaceLoad(this, true);">
            <i class="fa fa-compress" aria-hidden="true"></i>&nbsp;
            <?php echo $lang['pdf_compress_app_title']; ?>
        </a>
    </li>
    <?php
    
    return true;
}

/**
 * Add CSS and JavaScript for the application
 */
function HookPdf_compressAllAdditionalheaderhtml()
{
    global $baseurl;
    
    // Only load on PDF compression pages
    $current_page = basename($_SERVER['PHP_SELF']);
    $pdf_compress_pages = array('index.php', 'upload.php', 'compress.php', 'history.php', 'setup.php');
    
    // Check if we're in the PDF compress plugin directory
    $is_pdf_compress_page = (strpos($_SERVER['REQUEST_URI'], '/plugins/pdf_compress/') !== false);
    
    if ($is_pdf_compress_page || in_array($current_page, $pdf_compress_pages)) {
        ?>
        <link rel="stylesheet" type="text/css" href="<?php echo $baseurl; ?>/plugins/pdf_compress/css/style.css">
        <script type="text/javascript" src="<?php echo $baseurl; ?>/plugins/pdf_compress/js/pdf_compress.js"></script>
        <script type="text/javascript">
            // Initialize PDF Compression Application
            var pdfCompressApp = {
                baseUrl: '<?php echo $baseurl; ?>',
                apiUrl: '<?php echo $baseurl; ?>/plugins/pdf_compress/pages',
                maxFileSize: <?php echo $pdf_compress_max_file_size_mb; ?>,
                refreshInterval: <?php echo $pdf_compress_auto_refresh_interval; ?>,
                
                // Utility functions
                formatFileSize: function(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    var k = 1024;
                    var sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    var i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },
                
                showMessage: function(message, type) {
                    type = type || 'info';
                    var alertClass = 'alert-' + (type === 'error' ? 'danger' : type);
                    var html = '<div class="alert ' + alertClass + ' alert-dismissible">' +
                              '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                              message + '</div>';
                    $('#messages').html(html);
                },
                
                clearMessages: function() {
                    $('#messages').empty();
                }
            };
        </script>
        <?php
    }
}

/**
 * Add tools menu item if configured
 */
function HookPdf_compressAllToolsmenu()
{
    global $lang, $baseurl, $pdf_compress_menu_position, 
           $pdf_compress_allowed_user_groups, $usergroup, $username;
    
    // Only show in tools menu if configured
    if ($pdf_compress_menu_position !== 'tools') {
        return false;
    }
    
    // Check if user is logged in
    if (!isset($username)) {
        return false;
    }
    
    // Check user group restrictions
    if (!empty($pdf_compress_allowed_user_groups) && 
        !in_array($usergroup, $pdf_compress_allowed_user_groups)) {
        return false;
    }
    
    ?>
    <li>
        <a href="<?php echo $baseurl; ?>/plugins/pdf_compress/pages/index.php">
            <i class="fa fa-compress"></i>&nbsp;<?php echo $lang['pdf_compress_app_title']; ?>
        </a>
    </li>
    <?php
    
    return true;
}

/**
 * Add admin menu item if configured
 */
function HookPdf_compressAllAdminmenu()
{
    global $lang, $baseurl, $pdf_compress_menu_position, $usergroup;
    
    // Only show in admin menu if configured and user is admin
    if ($pdf_compress_menu_position !== 'admin' || !checkperm('a')) {
        return false;
    }
    
    ?>
    <li>
        <a href="<?php echo $baseurl; ?>/plugins/pdf_compress/pages/index.php">
            <i class="fa fa-compress"></i>&nbsp;<?php echo $lang['pdf_compress_app_title']; ?>
        </a>
    </li>
    <?php
    
    return true;
}
