<?php
/**
 * PDF Compression Application - Upload Page
 * 
 * File upload and compression options page
 */

include '../../../include/db.php';
include_once '../../../include/general.php';
include_once '../../../include/authenticate.php';

// Check if user is logged in
if (!isset($username)) {
    redirect('pages/login.php?url=' . urlencode($_SERVER['REQUEST_URI']));
    exit();
}

// Check user group permissions
if (!empty($pdf_compress_allowed_user_groups) && 
    !in_array($usergroup, $pdf_compress_allowed_user_groups)) {
    exit($lang['pdf_compress_error_permission_denied']);
}

// Include header
include '../../../include/header.php';
?>

<div class="BasicsBox">
    <h1>
        <i class="fa fa-upload fa-fw"></i>
        <?php echo $lang['pdf_compress_upload_title']; ?>
    </h1>
    <p><?php echo $lang['pdf_compress_upload_subtitle']; ?></p>
    <p class="help-text">
        <?php echo str_replace('{max_size}', $pdf_compress_max_file_size_mb, $lang['pdf_compress_upload_instructions']); ?>
    </p>
</div>

<!-- Messages area -->
<div id="messages"></div>

<!-- File Upload Section -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_select_file']; ?></h2>
    
    <div class="upload-area" id="upload-area">
        <div class="upload-content">
            <i class="fa fa-cloud-upload fa-3x"></i>
            <p><?php echo $lang['pdf_compress_drag_drop']; ?></p>
            <input type="file" id="file-input" accept=".pdf" style="display: none;">
            <button type="button" class="btn btn-primary" onclick="document.getElementById('file-input').click();">
                <i class="fa fa-folder-open"></i> <?php echo $lang['browse']; ?>
            </button>
        </div>
        
        <div class="file-info" id="file-info" style="display: none;">
            <div class="file-preview">
                <i class="fa fa-file-pdf-o fa-3x"></i>
            </div>
            <div class="file-details">
                <h3 id="file-name"></h3>
                <p><strong><?php echo $lang['pdf_compress_file_size']; ?>:</strong> <span id="file-size"></span></p>
                <p><strong><?php echo $lang['status']; ?>:</strong> <span id="file-status"><?php echo $lang['pdf_compress_file_selected']; ?></span></p>
            </div>
            <div class="file-actions">
                <button type="button" class="btn btn-sm" onclick="clearFile();">
                    <i class="fa fa-times"></i> <?php echo $lang['remove']; ?>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Compression Options -->
<div class="BasicsBox" id="compression-options" style="display: none;">
    <h2><?php echo $lang['pdf_compress_options_title']; ?></h2>
    
    <form id="compression-form">
        <div class="Question">
            <label for="method"><?php echo $lang['pdf_compress_method']; ?>:</label>
            <select name="method" id="method" class="stdwidth">
                <option value="auto" <?php echo ($pdf_compress_default_method === 'auto') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_auto']; ?>
                </option>
                <option value="pymupdf" <?php echo ($pdf_compress_default_method === 'pymupdf') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_pymupdf']; ?>
                </option>
                <option value="ghostscript" <?php echo ($pdf_compress_default_method === 'ghostscript') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_ghostscript']; ?>
                </option>
                <option value="pdf2image" <?php echo ($pdf_compress_default_method === 'pdf2image') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_pdf2image']; ?>
                </option>
            </select>
            <div class="clearerleft"></div>
            <div class="FormHelp"><?php echo $lang['pdf_compress_help_method']; ?></div>
        </div>
        
        <div class="Question">
            <label for="image_quality"><?php echo $lang['pdf_compress_image_quality']; ?>:</label>
            <div class="slider-container">
                <input type="range" name="image_quality" id="image_quality" 
                       min="1" max="100" value="<?php echo $pdf_compress_default_quality; ?>" 
                       class="quality-slider">
                <span class="slider-value" id="quality-value"><?php echo $pdf_compress_default_quality; ?></span>
            </div>
            <div class="clearerleft"></div>
            <div class="FormHelp"><?php echo $lang['pdf_compress_image_quality_desc']; ?></div>
        </div>
        
        <div class="Question">
            <label for="image_scale"><?php echo $lang['pdf_compress_image_scale']; ?>:</label>
            <div class="slider-container">
                <input type="range" name="image_scale" id="image_scale" 
                       min="0.1" max="1.0" step="0.1" value="<?php echo $pdf_compress_default_scale; ?>" 
                       class="scale-slider">
                <span class="slider-value" id="scale-value"><?php echo $pdf_compress_default_scale; ?></span>
            </div>
            <div class="clearerleft"></div>
            <div class="FormHelp"><?php echo $lang['pdf_compress_image_scale_desc']; ?></div>
        </div>
        
        <?php if ($pdf_compress_show_advanced_options): ?>
        <div class="advanced-options" id="advanced-options" style="display: none;">
            <div class="Question">
                <label for="target_size_mb"><?php echo $lang['pdf_compress_target_size']; ?>:</label>
                <input type="number" name="target_size_mb" id="target_size_mb" 
                       min="0.1" step="0.1" class="stdwidth" placeholder="<?php echo $lang['optional']; ?>">
                <div class="clearerleft"></div>
                <div class="FormHelp"><?php echo $lang['pdf_compress_target_size_desc']; ?></div>
            </div>
            
            <div class="Question">
                <label for="skip_pages"><?php echo $lang['pdf_compress_skip_pages']; ?>:</label>
                <input type="text" name="skip_pages" id="skip_pages" class="stdwidth" 
                       placeholder="e.g., 1,5,10-12">
                <div class="clearerleft"></div>
                <div class="FormHelp"><?php echo $lang['pdf_compress_skip_pages_desc']; ?></div>
            </div>
        </div>
        
        <div class="Question">
            <a href="#" id="toggle-advanced" class="advanced-toggle">
                <i class="fa fa-cog"></i> <?php echo $lang['pdf_compress_show_advanced']; ?>
            </a>
        </div>
        <?php endif; ?>
    </form>
</div>

<!-- Action Buttons -->
<div class="QuestionSubmit" id="action-buttons" style="display: none;">
    <input type="button" name="compress" id="start-compression" 
           value="<?php echo $lang['pdf_compress_upload_button']; ?>" class="btn btn-primary">
    <input type="button" name="back" value="<?php echo $lang['pdf_compress_back']; ?>" 
           onclick="CentralSpaceLoad('index.php', true);" class="btn">
</div>

<!-- Progress Section (initially hidden) -->
<div id="compression-progress" class="BasicsBox" style="display: none;">
    <h2><?php echo $lang['pdf_compress_progress_title']; ?></h2>
    
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="progress-info">
            <div class="progress-text" id="progress-text"><?php echo $lang['pdf_compress_progress_uploading']; ?></div>
            <div class="progress-percentage" id="progress-percentage">0%</div>
        </div>
    </div>
    
    <div class="progress-details" id="progress-details"></div>
    
    <div class="progress-actions">
        <button type="button" class="btn" id="cancel-compression">
            <i class="fa fa-times"></i> <?php echo $lang['pdf_compress_cancel']; ?>
        </button>
    </div>
</div>

<!-- Results Section (initially hidden) -->
<div id="compression-results" class="BasicsBox" style="display: none;">
    <h2><?php echo $lang['pdf_compress_results_title']; ?></h2>
    
    <div class="results-grid">
        <div class="result-item">
            <div class="result-icon">
                <i class="fa fa-file-pdf-o"></i>
            </div>
            <div class="result-content">
                <div class="result-label"><?php echo $lang['pdf_compress_original_size']; ?></div>
                <div class="result-value" id="original-size">-</div>
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-icon">
                <i class="fa fa-compress"></i>
            </div>
            <div class="result-content">
                <div class="result-label"><?php echo $lang['pdf_compress_compressed_size']; ?></div>
                <div class="result-value" id="compressed-size">-</div>
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-icon">
                <i class="fa fa-chart-line"></i>
            </div>
            <div class="result-content">
                <div class="result-label"><?php echo $lang['pdf_compress_compression_ratio']; ?></div>
                <div class="result-value" id="compression-ratio">-</div>
            </div>
        </div>
        
        <div class="result-item">
            <div class="result-icon">
                <i class="fa fa-arrow-down"></i>
            </div>
            <div class="result-content">
                <div class="result-label"><?php echo $lang['pdf_compress_size_reduction']; ?></div>
                <div class="result-value" id="size-reduction">-</div>
            </div>
        </div>
    </div>
    
    <div class="result-actions">
        <a href="#" id="download-link" class="btn btn-primary btn-lg" style="display: none;">
            <i class="fa fa-download"></i> <?php echo $lang['pdf_compress_download_file']; ?>
        </a>
        <button type="button" class="btn btn-lg" onclick="location.reload();">
            <i class="fa fa-refresh"></i> <?php echo $lang['pdf_compress_compress_another']; ?>
        </button>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    var selectedFile = null;
    var compressionInProgress = false;
    var statusCheckInterval = null;
    var currentJobId = null;
    
    // File input change handler
    $('#file-input').change(function() {
        handleFileSelect(this.files[0]);
    });
    
    // Drag and drop handlers
    $('#upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('drag-over');
    });
    
    $('#upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');
    });
    
    $('#upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('drag-over');
        
        var files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });
    
    // Slider value updates
    $('#image_quality').on('input', function() {
        $('#quality-value').text($(this).val());
    });
    
    $('#image_scale').on('input', function() {
        $('#scale-value').text($(this).val());
    });
    
    // Advanced options toggle
    $('#toggle-advanced').click(function(e) {
        e.preventDefault();
        var $options = $('#advanced-options');
        var $link = $(this);
        
        if ($options.is(':visible')) {
            $options.slideUp();
            $link.html('<i class="fa fa-cog"></i> <?php echo $lang['pdf_compress_show_advanced']; ?>');
        } else {
            $options.slideDown();
            $link.html('<i class="fa fa-cog"></i> <?php echo $lang['pdf_compress_hide_advanced']; ?>');
        }
    });
    
    // Start compression
    $('#start-compression').click(function() {
        if (!selectedFile || compressionInProgress) return;
        startCompression();
    });
    
    // Cancel compression
    $('#cancel-compression').click(function() {
        cancelCompression();
    });
    
    function handleFileSelect(file) {
        // Validate file type
        if (!file || file.type !== 'application/pdf') {
            pdfCompressApp.showMessage('<?php echo $lang['pdf_compress_error_invalid_file']; ?>', 'error');
            return;
        }
        
        // Validate file size
        var maxSize = <?php echo $pdf_compress_max_file_size_mb; ?> * 1024 * 1024;
        if (file.size > maxSize) {
            var message = '<?php echo $lang['pdf_compress_error_file_too_large']; ?>'
                .replace('{max_size}', '<?php echo $pdf_compress_max_file_size_mb; ?>');
            pdfCompressApp.showMessage(message, 'error');
            return;
        }
        
        selectedFile = file;
        
        // Update UI
        $('#file-name').text(file.name);
        $('#file-size').text(pdfCompressApp.formatFileSize(file.size));
        
        $('.upload-content').hide();
        $('#file-info').show();
        $('#compression-options').show();
        $('#action-buttons').show();
        
        // Set max target size
        var maxTargetSize = Math.floor(file.size / (1024 * 1024) * 10) / 10;
        $('#target_size_mb').attr('max', maxTargetSize);
        
        pdfCompressApp.clearMessages();
    }
    
    function clearFile() {
        selectedFile = null;
        $('#file-input').val('');
        
        $('.upload-content').show();
        $('#file-info').hide();
        $('#compression-options').hide();
        $('#action-buttons').hide();
    }
    
    function startCompression() {
        if (!selectedFile) return;
        
        compressionInProgress = true;
        
        // Hide upload sections and show progress
        $('#compression-options').hide();
        $('#action-buttons').hide();
        $('#compression-progress').show();
        
        // Prepare form data
        var formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('image_quality', $('#image_quality').val());
        formData.append('image_scale', $('#image_scale').val());
        formData.append('method', $('#method').val());
        
        var targetSize = $('#target_size_mb').val();
        if (targetSize) {
            formData.append('target_size_mb', targetSize);
        }
        
        var skipPages = $('#skip_pages').val();
        if (skipPages) {
            formData.append('skip_pages', skipPages);
        }
        
        // Start compression request
        $.ajax({
            url: 'ajax_compress.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            xhr: function() {
                var xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        var percentComplete = (e.loaded / e.total) * 100;
                        updateProgress(percentComplete, '<?php echo $lang['pdf_compress_progress_uploading']; ?>');
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                if (response.success) {
                    currentJobId = response.job_id;
                    startStatusChecking();
                } else {
                    showError(response.message || '<?php echo $lang['pdf_compress_error_compression_failed']; ?>');
                }
            },
            error: function() {
                showError('<?php echo $lang['pdf_compress_error_api_unavailable']; ?>');
            }
        });
    }
    
    function startStatusChecking() {
        statusCheckInterval = setInterval(function() {
            checkCompressionStatus();
        }, pdfCompressApp.refreshInterval);
    }
    
    function checkCompressionStatus() {
        if (!currentJobId) return;
        
        $.ajax({
            url: 'ajax_status.php',
            type: 'GET',
            data: { job_id: currentJobId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    updateProgress(data.progress || 0, data.message || '');
                    
                    if (data.current_page && data.total_pages) {
                        var details = '<?php echo $lang['pdf_compress_progress_processing']; ?>'
                            .replace('{current}', data.current_page)
                            .replace('{total}', data.total_pages);
                        $('#progress-details').text(details);
                    }
                    
                    if (data.status === 'completed') {
                        showResults(data);
                        stopStatusChecking();
                    } else if (data.status === 'failed') {
                        showError(data.error_details || '<?php echo $lang['pdf_compress_error_compression_failed']; ?>');
                        stopStatusChecking();
                    }
                }
            },
            error: function() {
                // Continue checking - temporary network issues shouldn't stop the process
            }
        });
    }
    
    function updateProgress(percentage, message) {
        $('#progress-fill').css('width', percentage + '%');
        $('#progress-percentage').text(Math.round(percentage) + '%');
        $('#progress-text').text(message);
    }
    
    function showResults(data) {
        $('#compression-progress').hide();
        $('#compression-results').show();
        
        // Update result values
        if (selectedFile) {
            $('#original-size').text(pdfCompressApp.formatFileSize(selectedFile.size));
        }
        
        if (data.compressed_size_mb) {
            $('#compressed-size').text(pdfCompressApp.formatFileSize(data.compressed_size_mb * 1024 * 1024));
        }
        
        if (data.compression_ratio) {
            $('#compression-ratio').text(data.compression_ratio.toFixed(1) + '%');
            
            var reduction = 100 - data.compression_ratio;
            $('#size-reduction').text(reduction.toFixed(1) + '%');
        }
        
        if (data.download_url) {
            $('#download-link').attr('href', data.download_url).show();
        }
        
        compressionInProgress = false;
        pdfCompressApp.showMessage('<?php echo $lang['pdf_compress_success_compression']; ?>', 'success');
    }
    
    function showError(message) {
        $('#compression-progress').hide();
        $('#compression-options').show();
        $('#action-buttons').show();
        
        pdfCompressApp.showMessage('<?php echo $lang['pdf_compress_error_title']; ?>: ' + message, 'error');
        compressionInProgress = false;
        stopStatusChecking();
    }
    
    function cancelCompression() {
        if (currentJobId) {
            // Optionally send cancel request to API
            $.ajax({
                url: 'ajax_cancel.php',
                type: 'POST',
                data: { job_id: currentJobId },
                dataType: 'json'
            });
        }
        
        stopStatusChecking();
        compressionInProgress = false;
        
        $('#compression-progress').hide();
        $('#compression-options').show();
        $('#action-buttons').show();
        
        pdfCompressApp.showMessage('<?php echo $lang['pdf_compress_cancelled']; ?>', 'info');
    }
    
    function stopStatusChecking() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }
    }
});
</script>

<?php
// Add missing language strings
$lang['optional'] = 'Optional';
$lang['remove'] = 'Remove';
$lang['browse'] = 'Browse';
$lang['pdf_compress_cancelled'] = 'Compression cancelled';

include '../../../include/footer.php';
?>
