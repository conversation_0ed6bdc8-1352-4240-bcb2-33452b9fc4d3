<?php
/**
 * PDF Compression Application Configuration
 * 
 * Configuration for standalone PDF compression application
 */

// API Configuration
$pdf_compress_api_url = 'http://localhost:8000';
$pdf_compress_api_key = 'pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1';

// Application Settings
$pdf_compress_app_title = 'PDF Compression Service';
$pdf_compress_app_description = 'Compress PDF files to reduce size while maintaining quality';
$pdf_compress_max_file_size_mb = 300;
$pdf_compress_allowed_extensions = array('pdf');

// Default Compression Settings
$pdf_compress_default_quality = 75;
$pdf_compress_default_scale = 0.8;
$pdf_compress_default_method = 'auto';

// UI Settings
$pdf_compress_show_advanced_options = true;
$pdf_compress_enable_progress_tracking = true;
$pdf_compress_auto_refresh_interval = 2000; // milliseconds
$pdf_compress_results_per_page = 20;

// User Access Control
$pdf_compress_allowed_user_groups = array(); // Empty = all groups allowed
$pdf_compress_require_login = true;

// File Management
$pdf_compress_temp_upload_dir = 'filestore/tmp/pdf_compress';
$pdf_compress_cleanup_temp_files = true;
$pdf_compress_temp_file_lifetime = 3600; // seconds

// API Timeout Settings
$pdf_compress_request_timeout = 300; // seconds
$pdf_compress_status_check_timeout = 30; // seconds

// Logging
$pdf_compress_enable_logging = true;
$pdf_compress_log_file = 'filestore/logs/pdf_compress.log';

// Navigation
$pdf_compress_show_in_main_menu = true;
$pdf_compress_menu_position = 'tools'; // 'admin', 'tools', or 'main'
