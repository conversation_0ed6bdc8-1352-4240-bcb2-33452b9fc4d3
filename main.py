import os
import shutil
import asyncio
from datetime import datetime
from typing import Dict, Optional
import aiofiles
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, Form, Depends
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from dotenv import load_dotenv

from models import (
    CompressionRequest, CompressionResponse, StatusResponse,
    HealthResponse, CompressionStatus, CompressionMethod
)
from compression import (
    compress_pdf_pymupdf, compress_pdf_ghostscript,
    compress_pdf_pdf2image, compress_pdf_auto
)
from utils import (
    parse_skip_pages, generate_unique_id,
    ensure_directory_exists, cleanup_old_files, validate_pdf_file, get_pdf_info
)
from auth import get_api_key

# Load environment variables
load_dotenv()

# Configuration
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "uploads")
OUTPUT_DIR = os.getenv("OUTPUT_DIR", "outputs")
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "300")) * 1024 * 1024  # 300MB default
CLEANUP_INTERVAL_HOURS = int(os.getenv("CLEANUP_INTERVAL_HOURS", "24"))
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")

# Ensure directories exist
ensure_directory_exists(UPLOAD_DIR)
ensure_directory_exists(OUTPUT_DIR)

# FastAPI app
app = FastAPI(
    title="PDF Compression Service",
    description="A secure service for compressing PDF files with various methods and options. Requires API key authentication.",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://platform.cofmodassets.net",
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for downloads
app.mount("/files", StaticFiles(directory=OUTPUT_DIR), name="files")

# In-memory job storage (in production, use Redis or database)
jobs: Dict[str, Dict] = {}


def update_job_status(job_id: str, status: CompressionStatus, **kwargs):
    """Update job status and additional information."""
    if job_id in jobs:
        jobs[job_id].update({
            "status": status,
            "updated_at": datetime.now(),
            **kwargs
        })


def progress_callback_factory(job_id: str):
    """Create a progress callback function for a specific job."""
    def callback(progress_data: dict):
        update_job_status(
            job_id,
            CompressionStatus.PROCESSING,
            current_page=progress_data.get("current_page"),
            total_pages=progress_data.get("total_pages"),
            progress=progress_data.get("progress", 0),
            message=progress_data.get("message", "Processing...")
        )
    return callback


async def compress_pdf_task(
    job_id: str,
    input_path: str,
    output_path: str,
    compression_request: CompressionRequest
):
    """Background task to compress PDF."""
    try:
        # Parse skip pages
        skip_pages = parse_skip_pages(compression_request.skip_pages or "")
        
        # Create progress callback
        progress_callback = progress_callback_factory(job_id)
        
        # Choose compression method
        if compression_request.method == CompressionMethod.PYMUPDF:
            result = compress_pdf_pymupdf(
                input_path, output_path,
                compression_request.image_quality,
                compression_request.image_scale,
                skip_pages,
                progress_callback
            )
        elif compression_request.method == CompressionMethod.GHOSTSCRIPT:
            result = compress_pdf_ghostscript(
                input_path, output_path,
                "/ebook",
                progress_callback
            )
        elif compression_request.method == CompressionMethod.PDF2IMAGE:
            result = compress_pdf_pdf2image(
                input_path, output_path,
                150,
                compression_request.image_quality,
                skip_pages,
                progress_callback
            )
        else:  # AUTO
            result = compress_pdf_auto(
                input_path, output_path,
                compression_request.image_quality,
                compression_request.image_scale,
                skip_pages,
                compression_request.target_size_mb,
                progress_callback
            )
        
        if result["success"]:
            # Generate download URL
            file_id = generate_unique_id()
            final_filename = f"{file_id}_compressed.pdf"
            final_path = os.path.join(OUTPUT_DIR, final_filename)

            # Move file to final location (use shutil.move for cross-device compatibility)
            try:
                shutil.move(output_path, final_path)
            except Exception as e:
                update_job_status(
                    job_id,
                    CompressionStatus.FAILED,
                    message="Failed to move compressed file",
                    error_details=f"File move error: {str(e)}"
                )
                return
            
            download_url = f"{BASE_URL}/download/{file_id}"
            
            update_job_status(
                job_id,
                CompressionStatus.COMPLETED,
                progress=100,
                message="Compression completed successfully",
                download_url=download_url,
                file_id=file_id,
                filename=final_filename,
                original_size_mb=result["initial_size_mb"],
                compressed_size_mb=result["final_size_mb"],
                compression_ratio=result["compression_ratio"]
            )
        else:
            update_job_status(
                job_id,
                CompressionStatus.FAILED,
                message="Compression failed",
                error_details=result.get("error", "Unknown error")
            )
    
    except Exception as e:
        update_job_status(
            job_id,
            CompressionStatus.FAILED,
            message="Compression failed with exception",
            error_details=str(e)
        )
    
    finally:
        # Clean up input file
        if os.path.exists(input_path):
            os.remove(input_path)


@app.post("/compress", response_model=CompressionResponse)
async def compress_pdf(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    image_quality: int = Form(75),
    image_scale: float = Form(0.8),
    skip_pages: Optional[str] = Form(None),
    method: CompressionMethod = Form(CompressionMethod.AUTO),
    target_size_mb: Optional[float] = Form(None),
    api_key: str = Depends(get_api_key)
):
    """Upload and compress a PDF file."""
    
    # Validate file
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")
    
    # Check file size
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=413, 
            detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
        )
    
    # Generate job ID and file paths
    job_id = generate_unique_id()
    input_filename = f"{job_id}_input.pdf"
    input_path = os.path.join(UPLOAD_DIR, input_filename)
    output_path = os.path.join(UPLOAD_DIR, f"{job_id}_output.pdf")
    
    # Save uploaded file
    async with aiofiles.open(input_path, 'wb') as f:
        await f.write(content)
    
    # Validate PDF
    if not validate_pdf_file(input_path):
        os.remove(input_path)
        raise HTTPException(status_code=400, detail="Invalid PDF file")
    
    # Get PDF info
    pdf_info = get_pdf_info(input_path)
    
    # Create job record
    jobs[job_id] = {
        "job_id": job_id,
        "status": CompressionStatus.PENDING,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "original_filename": file.filename,
        "original_size_mb": pdf_info["size_mb"],
        "total_pages": pdf_info["page_count"],
        "current_page": 0,
        "progress": 0,
        "message": "Job created, waiting to start"
    }
    
    # Create compression request
    compression_request = CompressionRequest(
        image_quality=image_quality,
        image_scale=image_scale,
        skip_pages=skip_pages,
        method=method,
        target_size_mb=target_size_mb
    )
    
    # Start background task
    background_tasks.add_task(
        compress_pdf_task,
        job_id,
        input_path,
        output_path,
        compression_request
    )
    
    return CompressionResponse(
        job_id=job_id,
        status=CompressionStatus.PENDING,
        message="PDF compression job started"
    )


@app.get("/status/{job_id}", response_model=StatusResponse)
async def get_job_status(job_id: str, api_key: str = Depends(get_api_key)):
    """Get the status of a compression job."""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")

    job = jobs[job_id]

    return StatusResponse(
        job_id=job_id,
        status=job["status"],
        progress=job.get("progress", 0),
        current_page=job.get("current_page"),
        total_pages=job.get("total_pages"),
        message=job.get("message", ""),
        download_url=job.get("download_url"),
        original_size_mb=job.get("original_size_mb"),
        compressed_size_mb=job.get("compressed_size_mb"),
        compression_ratio=job.get("compression_ratio"),
        error_details=job.get("error_details")
    )


@app.get("/download/{file_id}")
async def download_file(file_id: str, api_key: str = Depends(get_api_key)):
    """Download a compressed PDF file."""
    # Find the job with this file_id
    job = None
    for j in jobs.values():
        if j.get("file_id") == file_id:
            job = j
            break

    if not job:
        raise HTTPException(status_code=404, detail="File not found")

    filename = job.get("filename")
    if not filename:
        raise HTTPException(status_code=404, detail="File not available")

    file_path = os.path.join(OUTPUT_DIR, filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found on disk")

    # Return file with original name but compressed suffix
    original_name = job.get("original_filename", "compressed.pdf")
    download_name = original_name.replace(".pdf", "_compressed.pdf")

    return FileResponse(
        path=file_path,
        filename=download_name,
        media_type="application/pdf"
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""

    # Check dependencies
    dependencies = {}

    try:
        import fitz
        dependencies["pymupdf"] = "available"
    except ImportError:
        dependencies["pymupdf"] = "not available"

    try:
        import subprocess
        import sys
        gs_cmd = "gswin64c" if sys.platform == "win32" else "gs"
        result = subprocess.run([gs_cmd, "--version"], capture_output=True, timeout=5)
        dependencies["ghostscript"] = "available" if result.returncode == 0 else "not available"
    except:
        dependencies["ghostscript"] = "not available"

    try:
        from pdf2image import convert_from_path
        dependencies["pdf2image"] = "available"
    except ImportError:
        dependencies["pdf2image"] = "not available"

    return HealthResponse(
        status="healthy",
        version="1.0.0",
        dependencies=dependencies
    )


@app.on_event("startup")
async def startup_event():
    """Startup event to initialize the application."""
    print("PDF Compression Service starting up...")

    # Clean up old files on startup
    cleanup_old_files(UPLOAD_DIR, CLEANUP_INTERVAL_HOURS)
    cleanup_old_files(OUTPUT_DIR, CLEANUP_INTERVAL_HOURS)

    print(f"Upload directory: {UPLOAD_DIR}")
    print(f"Output directory: {OUTPUT_DIR}")
    print(f"Max file size: {MAX_FILE_SIZE // (1024*1024)}MB")


@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event to clean up resources."""
    print("PDF Compression Service shutting down...")


# Periodic cleanup task
async def periodic_cleanup():
    """Periodically clean up old files."""
    while True:
        await asyncio.sleep(3600)  # Run every hour
        cleanup_old_files(UPLOAD_DIR, CLEANUP_INTERVAL_HOURS)
        cleanup_old_files(OUTPUT_DIR, CLEANUP_INTERVAL_HOURS)


@app.on_event("startup")
async def start_cleanup_task():
    """Start the periodic cleanup task."""
    asyncio.create_task(periodic_cleanup())


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
