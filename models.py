from pydantic import BaseModel, Field
from typing import Optional, List
from enum import Enum


class CompressionMethod(str, Enum):
    PYMUPDF = "pymupdf"
    GHOSTSCRIPT = "ghostscript"
    PDF2IMAGE = "pdf2image"
    AUTO = "auto"


class CompressionStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class CompressionRequest(BaseModel):
    image_quality: int = Field(default=75, ge=1, le=100, description="JPEG quality (1-100)")
    image_scale: float = Field(default=0.8, ge=0.1, le=1.0, description="Scale factor for large images")
    skip_pages: Optional[str] = Field(default=None, description="Comma-separated pages to skip (e.g., '1,5,10-12')")
    method: CompressionMethod = Field(default=CompressionMethod.AUTO, description="Compression method to use")
    target_size_mb: Optional[float] = Field(default=None, ge=0.1, description="Target file size in MB (optional)")


class CompressionResponse(BaseModel):
    job_id: str
    status: CompressionStatus
    message: str


class StatusResponse(BaseModel):
    job_id: str
    status: CompressionStatus
    progress: float = Field(ge=0, le=100, description="Progress percentage")
    current_page: Optional[int] = None
    total_pages: Optional[int] = None
    message: str
    download_url: Optional[str] = None
    original_size_mb: Optional[float] = None
    compressed_size_mb: Optional[float] = None
    compression_ratio: Optional[float] = None
    error_details: Optional[str] = None


class DownloadResponse(BaseModel):
    file_id: str
    filename: str
    size_mb: float
    compression_ratio: Optional[float] = None


class HealthResponse(BaseModel):
    status: str
    version: str
    dependencies: dict
