import re
import os
import uuid
import shutil
from typing import List, Set
from pathlib import Path


def parse_skip_pages(skip_pages_str: str) -> Set[int]:
    """
    Parse comma-separated page numbers and ranges into a set of page numbers.
    
    Examples:
        "1,5,10-12" -> {1, 5, 10, 11, 12}
        "1,3,5" -> {1, 3, 5}
        "10-15" -> {10, 11, 12, 13, 14, 15}
    
    Args:
        skip_pages_str: String containing page numbers and ranges
        
    Returns:
        Set of page numbers to skip (1-based indexing)
    """
    if not skip_pages_str or not skip_pages_str.strip():
        return set()
    
    pages_to_skip = set()
    
    # Split by comma and process each part
    parts = [part.strip() for part in skip_pages_str.split(',')]
    
    for part in parts:
        if not part:
            continue
            
        # Check if it's a range (e.g., "10-12")
        if '-' in part:
            try:
                start, end = part.split('-', 1)
                start_page = int(start.strip())
                end_page = int(end.strip())
                
                if start_page <= end_page:
                    pages_to_skip.update(range(start_page, end_page + 1))
                else:
                    # Handle reverse range
                    pages_to_skip.update(range(end_page, start_page + 1))
            except ValueError:
                # Invalid range format, skip this part
                continue
        else:
            # Single page number
            try:
                page_num = int(part.strip())
                if page_num > 0:  # Only positive page numbers
                    pages_to_skip.add(page_num)
            except ValueError:
                # Invalid page number, skip this part
                continue
    
    return pages_to_skip


def generate_unique_id() -> str:
    """Generate a unique ID for jobs and files."""
    return str(uuid.uuid4())


def get_file_size_mb(file_path: str) -> float:
    """Get file size in megabytes."""
    if os.path.exists(file_path):
        return os.path.getsize(file_path) / (1024 * 1024)
    return 0.0


def ensure_directory_exists(directory: str) -> None:
    """Ensure a directory exists, create if it doesn't."""
    Path(directory).mkdir(parents=True, exist_ok=True)


def cleanup_file(file_path: str) -> None:
    """Safely remove a file if it exists."""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception:
        pass  # Ignore cleanup errors


def cleanup_old_files(directory: str, max_age_hours: int = 24) -> None:
    """Remove files older than specified hours."""
    import time
    
    if not os.path.exists(directory):
        return
    
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        try:
            if os.path.isfile(file_path):
                file_age = current_time - os.path.getmtime(file_path)
                if file_age > max_age_seconds:
                    os.remove(file_path)
        except Exception:
            pass  # Ignore cleanup errors


def validate_pdf_file(file_path: str) -> bool:
    """Validate if a file is a valid PDF."""
    try:
        import fitz  # PyMuPDF
        doc = fitz.open(file_path)
        page_count = len(doc)
        doc.close()
        return page_count > 0
    except Exception:
        return False


def get_pdf_info(file_path: str) -> dict:
    """Get basic information about a PDF file."""
    try:
        import fitz  # PyMuPDF
        doc = fitz.open(file_path)
        info = {
            "page_count": len(doc),
            "size_mb": get_file_size_mb(file_path),
            "title": doc.metadata.get("title", ""),
            "author": doc.metadata.get("author", ""),
            "subject": doc.metadata.get("subject", ""),
        }
        doc.close()
        return info
    except Exception:
        return {
            "page_count": 0,
            "size_mb": get_file_size_mb(file_path),
            "title": "",
            "author": "",
            "subject": "",
        }
