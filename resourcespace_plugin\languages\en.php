<?php
/**
 * PDF Compress Plugin - English Language File
 */

// Plugin Name and Description
$lang['pdf_compress_plugin_name'] = 'PDF Compression';
$lang['pdf_compress_plugin_description'] = 'Compress PDF files using external compression service';

// Resource Tools
$lang['pdf_compress_tool_title'] = 'Compress PDF';
$lang['pdf_compress_tool_description'] = 'Reduce PDF file size using advanced compression';

// Compression Interface
$lang['pdf_compress_title'] = 'PDF Compression';
$lang['pdf_compress_subtitle'] = 'Compress your PDF file to reduce size while maintaining quality';
$lang['pdf_compress_current_file'] = 'Current File';
$lang['pdf_compress_file_size'] = 'File Size';
$lang['pdf_compress_page_count'] = 'Pages';

// Compression Options
$lang['pdf_compress_options_title'] = 'Compression Options';
$lang['pdf_compress_method'] = 'Compression Method';
$lang['pdf_compress_method_auto'] = 'Auto (Recommended)';
$lang['pdf_compress_method_pymupdf'] = 'PyMuPDF (Fast)';
$lang['pdf_compress_method_ghostscript'] = 'Ghostscript (High Quality)';
$lang['pdf_compress_method_pdf2image'] = 'PDF2Image (Maximum Compression)';

$lang['pdf_compress_image_quality'] = 'Image Quality';
$lang['pdf_compress_image_quality_desc'] = 'JPEG quality for images (1-100, higher = better quality)';
$lang['pdf_compress_image_scale'] = 'Image Scale';
$lang['pdf_compress_image_scale_desc'] = 'Scale factor for large images (0.1-1.0, lower = smaller size)';
$lang['pdf_compress_target_size'] = 'Target Size (MB)';
$lang['pdf_compress_target_size_desc'] = 'Desired file size in MB (optional)';
$lang['pdf_compress_skip_pages'] = 'Skip Pages';
$lang['pdf_compress_skip_pages_desc'] = 'Pages to skip compression (e.g., "1,5,10-12")';

// Actions
$lang['pdf_compress_start_compression'] = 'Start Compression';
$lang['pdf_compress_cancel'] = 'Cancel';
$lang['pdf_compress_download'] = 'Download Compressed File';
$lang['pdf_compress_create_alternative'] = 'Create as Alternative File';
$lang['pdf_compress_replace_original'] = 'Replace Original File';

// Progress and Status
$lang['pdf_compress_status_pending'] = 'Pending';
$lang['pdf_compress_status_processing'] = 'Processing';
$lang['pdf_compress_status_completed'] = 'Completed';
$lang['pdf_compress_status_failed'] = 'Failed';

$lang['pdf_compress_progress_title'] = 'Compression Progress';
$lang['pdf_compress_progress_uploading'] = 'Uploading file...';
$lang['pdf_compress_progress_processing'] = 'Processing page {current} of {total}';
$lang['pdf_compress_progress_downloading'] = 'Preparing download...';
$lang['pdf_compress_progress_complete'] = 'Compression completed successfully!';

// Results
$lang['pdf_compress_results_title'] = 'Compression Results';
$lang['pdf_compress_original_size'] = 'Original Size';
$lang['pdf_compress_compressed_size'] = 'Compressed Size';
$lang['pdf_compress_compression_ratio'] = 'Compression Ratio';
$lang['pdf_compress_size_reduction'] = 'Size Reduction';

// Error Messages
$lang['pdf_compress_error_title'] = 'Compression Error';
$lang['pdf_compress_error_not_pdf'] = 'This resource is not a PDF file';
$lang['pdf_compress_error_file_too_large'] = 'File is too large for compression (maximum: {max_size}MB)';
$lang['pdf_compress_error_api_unavailable'] = 'Compression service is currently unavailable';
$lang['pdf_compress_error_invalid_response'] = 'Invalid response from compression service';
$lang['pdf_compress_error_compression_failed'] = 'Compression failed: {error}';
$lang['pdf_compress_error_download_failed'] = 'Failed to download compressed file';
$lang['pdf_compress_error_permission_denied'] = 'You do not have permission to compress this resource';
$lang['pdf_compress_error_invalid_parameters'] = 'Invalid compression parameters';
$lang['pdf_compress_error_timeout'] = 'Compression request timed out';

// Configuration
$lang['pdf_compress_config_title'] = 'PDF Compression Configuration';
$lang['pdf_compress_config_api_settings'] = 'API Settings';
$lang['pdf_compress_config_api_url'] = 'API URL';
$lang['pdf_compress_config_api_key'] = 'API Key';
$lang['pdf_compress_config_test_connection'] = 'Test Connection';

$lang['pdf_compress_config_compression_settings'] = 'Default Compression Settings';
$lang['pdf_compress_config_ui_settings'] = 'User Interface Settings';
$lang['pdf_compress_config_show_advanced'] = 'Show Advanced Options';
$lang['pdf_compress_config_enable_progress'] = 'Enable Progress Tracking';
$lang['pdf_compress_config_refresh_interval'] = 'Progress Refresh Interval (ms)';

$lang['pdf_compress_config_restrictions'] = 'Access Restrictions';
$lang['pdf_compress_config_allowed_resource_types'] = 'Allowed Resource Types';
$lang['pdf_compress_config_allowed_user_groups'] = 'Allowed User Groups';
$lang['pdf_compress_config_max_file_size'] = 'Maximum File Size (MB)';

$lang['pdf_compress_config_file_management'] = 'File Management';
$lang['pdf_compress_config_create_alternative'] = 'Create Alternative Files by Default';
$lang['pdf_compress_config_cleanup_temp'] = 'Cleanup Temporary Files';
$lang['pdf_compress_config_temp_lifetime'] = 'Temporary File Lifetime (seconds)';

// Success Messages
$lang['pdf_compress_success_config_saved'] = 'Configuration saved successfully';
$lang['pdf_compress_success_connection_test'] = 'Connection to compression service successful';
$lang['pdf_compress_success_compression_started'] = 'Compression started successfully';
$lang['pdf_compress_success_alternative_created'] = 'Compressed file created as alternative';
$lang['pdf_compress_success_original_replaced'] = 'Original file replaced with compressed version';

// Tooltips and Help
$lang['pdf_compress_help_method'] = 'Auto method automatically selects the best compression approach based on your file';
$lang['pdf_compress_help_quality'] = 'Higher quality preserves image detail but results in larger files';
$lang['pdf_compress_help_scale'] = 'Lower scale values reduce image dimensions for smaller file sizes';
$lang['pdf_compress_help_target_size'] = 'The system will attempt to achieve this target size through multiple passes';
$lang['pdf_compress_help_skip_pages'] = 'Useful for preserving high-quality images on specific pages like covers';

// Admin Messages
$lang['pdf_compress_admin_plugin_enabled'] = 'PDF Compression plugin is enabled and configured';
$lang['pdf_compress_admin_plugin_disabled'] = 'PDF Compression plugin is disabled';
$lang['pdf_compress_admin_config_required'] = 'PDF Compression plugin requires configuration';
$lang['pdf_compress_admin_api_error'] = 'Cannot connect to PDF compression service';

// Log Messages
$lang['pdf_compress_log_compression_started'] = 'PDF compression started for resource {resource_id}';
$lang['pdf_compress_log_compression_completed'] = 'PDF compression completed for resource {resource_id}';
$lang['pdf_compress_log_compression_failed'] = 'PDF compression failed for resource {resource_id}: {error}';
$lang['pdf_compress_log_alternative_created'] = 'Compressed alternative file created for resource {resource_id}';
$lang['pdf_compress_log_original_replaced'] = 'Original file replaced for resource {resource_id}';
