"""
Authentication module for PDF Compression Service
"""

import os
import secrets
from typing import <PERSON><PERSON>
from fastapi import HTTPEx<PERSON>, Security, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Security scheme
security = HTTPBearer()

# Get API key from environment
API_KEY = os.getenv("API_KEY")

if not API_KEY:
    raise ValueError("API_KEY environment variable is required")


def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)) -> str:
    """
    Verify the API key from the Authorization header.
    
    Args:
        credentials: HTTP Bearer credentials from the request header
        
    Returns:
        The API key if valid
        
    Raises:
        HTTPException: If the API key is invalid or missing
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header is required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if credentials.credentials != API_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return credentials.credentials


def generate_api_key(length: int = 64) -> str:
    """
    Generate a secure API key.
    
    Args:
        length: Length of the API key
        
    Returns:
        A secure random API key
    """
    return secrets.token_urlsafe(length)


def verify_api_key_header(api_key: Optional[str]) -> bool:
    """
    Verify API key from X-API-Key header (alternative method).
    
    Args:
        api_key: API key from X-API-Key header
        
    Returns:
        True if valid, False otherwise
    """
    if not api_key:
        return False
    
    return api_key == API_KEY


# Optional: API key dependency for routes that need authentication
def get_api_key(credentials: HTTPAuthorizationCredentials = Security(security)) -> str:
    """
    Dependency to get and verify API key.
    Use this as a dependency in your FastAPI routes.
    """
    return verify_api_key(credentials)
