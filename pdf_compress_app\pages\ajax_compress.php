<?php
/**
 * PDF Compression Application - AJAX Compression Handler
 * 
 * Handles file upload and compression requests to the external API
 */

include '../../../include/db.php';
include_once '../../../include/general.php';
include_once '../../../include/authenticate.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($username)) {
    echo json_encode([
        'success' => false,
        'message' => $lang['error-permissiondenied']
    ]);
    exit();
}

// Check user group permissions
if (!empty($pdf_compress_allowed_user_groups) && 
    !in_array($usergroup, $pdf_compress_allowed_user_groups)) {
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_permission_denied']
    ]);
    exit();
}

// Check if file was uploaded
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_upload_failed']
    ]);
    exit();
}

$uploaded_file = $_FILES['file'];

// Validate file type
$file_info = pathinfo($uploaded_file['name']);
$file_extension = strtolower($file_info['extension']);

if ($file_extension !== 'pdf') {
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_invalid_file']
    ]);
    exit();
}

// Validate file size
if ($uploaded_file['size'] > ($pdf_compress_max_file_size_mb * 1024 * 1024)) {
    echo json_encode([
        'success' => false,
        'message' => str_replace('{max_size}', $pdf_compress_max_file_size_mb, $lang['pdf_compress_error_file_too_large'])
    ]);
    exit();
}

// Get compression parameters
$image_quality = (int)getvalescaped('image_quality', $pdf_compress_default_quality);
$image_scale = (float)getvalescaped('image_scale', $pdf_compress_default_scale);
$method = getvalescaped('method', $pdf_compress_default_method);
$target_size_mb = getvalescaped('target_size_mb', '');
$skip_pages = getvalescaped('skip_pages', '');

// Validate parameters
if ($image_quality < 1 || $image_quality > 100) {
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_invalid_parameters']
    ]);
    exit();
}

if ($image_scale < 0.1 || $image_scale > 1.0) {
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_invalid_parameters']
    ]);
    exit();
}

$allowed_methods = ['auto', 'pymupdf', 'ghostscript', 'pdf2image'];
if (!in_array($method, $allowed_methods)) {
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_invalid_parameters']
    ]);
    exit();
}

try {
    // Prepare API request
    $api_url = rtrim($pdf_compress_api_url, '/') . '/compress';
    
    // Create cURL file upload
    $curl_file = new CURLFile($uploaded_file['tmp_name'], 'application/pdf', $uploaded_file['name']);
    
    $post_data = [
        'file' => $curl_file,
        'image_quality' => $image_quality,
        'image_scale' => $image_scale,
        'method' => $method
    ];
    
    // Add optional parameters
    if (!empty($target_size_mb) && is_numeric($target_size_mb)) {
        $post_data['target_size_mb'] = (float)$target_size_mb;
    }
    
    if (!empty($skip_pages)) {
        $post_data['skip_pages'] = $skip_pages;
    }
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $api_url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $post_data,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $pdf_compress_request_timeout,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $pdf_compress_api_key,
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false, // For development - should be true in production
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);
    
    // Execute request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    // Check for cURL errors
    if ($curl_error) {
        pdf_compress_log_error("cURL error: " . $curl_error);
        echo json_encode([
            'success' => false,
            'message' => $lang['pdf_compress_error_api_unavailable']
        ]);
        exit();
    }
    
    // Check HTTP response code
    if ($http_code !== 200) {
        pdf_compress_log_error("HTTP error: " . $http_code . " - " . $response);
        echo json_encode([
            'success' => false,
            'message' => $lang['pdf_compress_error_api_unavailable']
        ]);
        exit();
    }
    
    // Parse JSON response
    $api_response = json_decode($response, true);
    
    if (!$api_response) {
        pdf_compress_log_error("Invalid JSON response: " . $response);
        echo json_encode([
            'success' => false,
            'message' => $lang['pdf_compress_error_invalid_response']
        ]);
        exit();
    }
    
    // Check API response status
    if (!isset($api_response['job_id']) || !isset($api_response['status'])) {
        pdf_compress_log_error("Missing job_id or status in API response: " . $response);
        echo json_encode([
            'success' => false,
            'message' => $lang['pdf_compress_error_invalid_response']
        ]);
        exit();
    }
    
    // Store job information in session or database
    $job_data = [
        'job_id' => $api_response['job_id'],
        'filename' => $uploaded_file['name'],
        'original_size' => $uploaded_file['size'],
        'user' => $username,
        'created_date' => date('Y-m-d H:i:s'),
        'status' => $api_response['status'],
        'compression_params' => [
            'image_quality' => $image_quality,
            'image_scale' => $image_scale,
            'method' => $method,
            'target_size_mb' => $target_size_mb,
            'skip_pages' => $skip_pages
        ]
    ];
    
    // Store in session for now (in production, use database)
    if (!isset($_SESSION['pdf_compress_jobs'])) {
        $_SESSION['pdf_compress_jobs'] = [];
    }
    $_SESSION['pdf_compress_jobs'][$api_response['job_id']] = $job_data;
    
    // Log successful compression start
    pdf_compress_log_info("Compression started for user $username, job ID: " . $api_response['job_id']);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'job_id' => $api_response['job_id'],
        'status' => $api_response['status'],
        'message' => $api_response['message'] ?? $lang['pdf_compress_success_compression_started']
    ]);
    
} catch (Exception $e) {
    pdf_compress_log_error("Exception in compression: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $lang['pdf_compress_error_server']
    ]);
}

/**
 * Helper function to log errors
 */
function pdf_compress_log_error($message) {
    global $pdf_compress_enable_logging, $pdf_compress_log_file;
    
    if ($pdf_compress_enable_logging) {
        $log_entry = date('Y-m-d H:i:s') . " [ERROR] " . $message . "\n";
        error_log($log_entry, 3, $pdf_compress_log_file);
    }
}

/**
 * Helper function to log info
 */
function pdf_compress_log_info($message) {
    global $pdf_compress_enable_logging, $pdf_compress_log_file;
    
    if ($pdf_compress_enable_logging) {
        $log_entry = date('Y-m-d H:i:s') . " [INFO] " . $message . "\n";
        error_log($log_entry, 3, $pdf_compress_log_file);
    }
}
?>
