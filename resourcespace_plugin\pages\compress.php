<?php
/**
 * PDF Compress Plugin - Compression Interface
 * 
 * Main compression interface page
 */

include '../../../include/db.php';
include_once '../../../include/general.php';
include_once '../../../include/resource_functions.php';
include_once '../../../include/authenticate.php';

// Check if user is logged in
if (!isset($username)) {
    exit($lang['error-permissiondenied']);
}

// Get resource reference
$ref = getvalescaped('ref', '', true);
if (!$ref) {
    exit($lang['error-invalidresource']);
}

// Get resource data
$resource = get_resource_data($ref);
if (!$resource) {
    exit($lang['error-invalidresource']);
}

// Check permissions
if (!get_edit_access($ref)) {
    exit($lang['pdf_compress_error_permission_denied']);
}

// Check if this is a PDF
if (strtolower($resource['file_extension']) !== 'pdf') {
    exit($lang['pdf_compress_error_not_pdf']);
}

// Get file information
$file_path = get_resource_path($ref, true, '', false, $resource['file_extension']);
if (!file_exists($file_path)) {
    exit($lang['error-filenotfound']);
}

$file_size_bytes = filesize($file_path);
$file_size_mb = round($file_size_bytes / (1024 * 1024), 2);

// Check file size limit
global $pdf_compress_max_file_size_mb;
if ($file_size_mb > $pdf_compress_max_file_size_mb) {
    exit(str_replace('{max_size}', $pdf_compress_max_file_size_mb, $lang['pdf_compress_error_file_too_large']));
}

// Get PDF page count
$page_count = pdf_compress_get_page_count($file_path);

// Include header
include '../../../include/header.php';
?>

<div class="BasicsBox">
    <h1><?php echo $lang['pdf_compress_title']; ?></h1>
    <p><?php echo $lang['pdf_compress_subtitle']; ?></p>
</div>

<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_current_file']; ?></h2>
    
    <div class="file-info">
        <div class="file-preview">
            <?php if (isset($resource['has_image']) && $resource['has_image']): ?>
                <img src="<?php echo get_resource_path($ref, false, 'thm', false, $resource['preview_extension']); ?>" 
                     alt="<?php echo htmlspecialchars($resource['field8']); ?>" class="pdf-thumbnail">
            <?php else: ?>
                <div class="no-preview">
                    <i class="fa fa-file-pdf-o fa-3x"></i>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="file-details">
            <h3><?php echo htmlspecialchars($resource['field8']); ?></h3>
            <p><strong><?php echo $lang['pdf_compress_file_size']; ?>:</strong> <?php echo formatfilesize($file_size_bytes); ?></p>
            <p><strong><?php echo $lang['pdf_compress_page_count']; ?>:</strong> <?php echo $page_count; ?></p>
            <p><strong><?php echo $lang['resourcetype']; ?>:</strong> <?php echo $resource['resource_type_name']; ?></p>
        </div>
    </div>
</div>

<form id="compression-form" method="post" action="ajax_compress.php">
    <input type="hidden" name="ref" value="<?php echo $ref; ?>">
    
    <div class="BasicsBox">
        <h2><?php echo $lang['pdf_compress_options_title']; ?></h2>
        
        <div class="Question">
            <label for="method"><?php echo $lang['pdf_compress_method']; ?>:</label>
            <select name="method" id="method" class="stdwidth">
                <option value="auto" <?php echo ($pdf_compress_default_method === 'auto') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_auto']; ?>
                </option>
                <option value="pymupdf" <?php echo ($pdf_compress_default_method === 'pymupdf') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_pymupdf']; ?>
                </option>
                <option value="ghostscript" <?php echo ($pdf_compress_default_method === 'ghostscript') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_ghostscript']; ?>
                </option>
                <option value="pdf2image" <?php echo ($pdf_compress_default_method === 'pdf2image') ? 'selected' : ''; ?>>
                    <?php echo $lang['pdf_compress_method_pdf2image']; ?>
                </option>
            </select>
            <div class="clearerleft"></div>
        </div>
        
        <div class="Question">
            <label for="image_quality"><?php echo $lang['pdf_compress_image_quality']; ?>:</label>
            <input type="range" name="image_quality" id="image_quality" 
                   min="1" max="100" value="<?php echo $pdf_compress_default_quality; ?>" 
                   class="quality-slider">
            <span id="quality-value"><?php echo $pdf_compress_default_quality; ?></span>
            <div class="clearerleft"></div>
            <div class="FormHelp"><?php echo $lang['pdf_compress_image_quality_desc']; ?></div>
        </div>
        
        <div class="Question">
            <label for="image_scale"><?php echo $lang['pdf_compress_image_scale']; ?>:</label>
            <input type="range" name="image_scale" id="image_scale" 
                   min="0.1" max="1.0" step="0.1" value="<?php echo $pdf_compress_default_scale; ?>" 
                   class="scale-slider">
            <span id="scale-value"><?php echo $pdf_compress_default_scale; ?></span>
            <div class="clearerleft"></div>
            <div class="FormHelp"><?php echo $lang['pdf_compress_image_scale_desc']; ?></div>
        </div>
        
        <?php if ($pdf_compress_show_advanced_options): ?>
        <div class="advanced-options" style="display: none;">
            <div class="Question">
                <label for="target_size_mb"><?php echo $lang['pdf_compress_target_size']; ?>:</label>
                <input type="number" name="target_size_mb" id="target_size_mb" 
                       min="0.1" max="<?php echo $file_size_mb; ?>" step="0.1" class="stdwidth">
                <div class="clearerleft"></div>
                <div class="FormHelp"><?php echo $lang['pdf_compress_target_size_desc']; ?></div>
            </div>
            
            <div class="Question">
                <label for="skip_pages"><?php echo $lang['pdf_compress_skip_pages']; ?>:</label>
                <input type="text" name="skip_pages" id="skip_pages" class="stdwidth" 
                       placeholder="e.g., 1,5,10-12">
                <div class="clearerleft"></div>
                <div class="FormHelp"><?php echo $lang['pdf_compress_skip_pages_desc']; ?></div>
            </div>
        </div>
        
        <div class="Question">
            <a href="#" id="toggle-advanced" class="advanced-toggle">
                <i class="fa fa-cog"></i> <?php echo $lang['advanced']; ?>
            </a>
        </div>
        <?php endif; ?>
        
        <div class="Question">
            <label>
                <input type="checkbox" name="create_alternative" id="create_alternative" 
                       <?php echo $pdf_compress_create_alternative ? 'checked' : ''; ?>>
                <?php echo $lang['pdf_compress_create_alternative']; ?>
            </label>
            <div class="clearerleft"></div>
        </div>
    </div>
    
    <div class="QuestionSubmit">
        <input type="button" name="compress" id="start-compression" 
               value="<?php echo $lang['pdf_compress_start_compression']; ?>" class="btn btn-primary">
        <input type="button" name="cancel" value="<?php echo $lang['pdf_compress_cancel']; ?>" 
               onclick="ModalClose();" class="btn">
    </div>
</form>

<!-- Progress Section (initially hidden) -->
<div id="compression-progress" class="BasicsBox" style="display: none;">
    <h2><?php echo $lang['pdf_compress_progress_title']; ?></h2>
    
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="progress-text" id="progress-text"><?php echo $lang['pdf_compress_progress_uploading']; ?></div>
        <div class="progress-percentage" id="progress-percentage">0%</div>
    </div>
    
    <div class="progress-details" id="progress-details"></div>
</div>

<!-- Results Section (initially hidden) -->
<div id="compression-results" class="BasicsBox" style="display: none;">
    <h2><?php echo $lang['pdf_compress_results_title']; ?></h2>
    
    <div class="results-container">
        <div class="result-item">
            <span class="result-label"><?php echo $lang['pdf_compress_original_size']; ?>:</span>
            <span class="result-value" id="original-size"><?php echo formatfilesize($file_size_bytes); ?></span>
        </div>
        <div class="result-item">
            <span class="result-label"><?php echo $lang['pdf_compress_compressed_size']; ?>:</span>
            <span class="result-value" id="compressed-size">-</span>
        </div>
        <div class="result-item">
            <span class="result-label"><?php echo $lang['pdf_compress_compression_ratio']; ?>:</span>
            <span class="result-value" id="compression-ratio">-</span>
        </div>
        <div class="result-item">
            <span class="result-label"><?php echo $lang['pdf_compress_size_reduction']; ?>:</span>
            <span class="result-value" id="size-reduction">-</span>
        </div>
    </div>
    
    <div class="result-actions">
        <a href="#" id="download-link" class="btn btn-primary" style="display: none;">
            <i class="fa fa-download"></i> <?php echo $lang['pdf_compress_download']; ?>
        </a>
    </div>
</div>

<script type="text/javascript">
// PDF Compression Interface JavaScript
jQuery(document).ready(function($) {
    var compressionInProgress = false;
    var statusCheckInterval;
    var currentJobId = null;
    
    // Update slider values
    $('#image_quality').on('input', function() {
        $('#quality-value').text($(this).val());
    });
    
    $('#image_scale').on('input', function() {
        $('#scale-value').text($(this).val());
    });
    
    // Toggle advanced options
    $('#toggle-advanced').click(function(e) {
        e.preventDefault();
        $('.advanced-options').slideToggle();
    });
    
    // Start compression
    $('#start-compression').click(function() {
        if (compressionInProgress) return;
        
        startCompression();
    });
    
    function startCompression() {
        compressionInProgress = true;
        
        // Hide form and show progress
        $('#compression-form').hide();
        $('#compression-progress').show();
        
        // Prepare form data
        var formData = new FormData($('#compression-form')[0]);
        
        // Start compression request
        $.ajax({
            url: 'ajax_compress.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    currentJobId = response.job_id;
                    startStatusChecking();
                } else {
                    showError(response.message || '<?php echo $lang['pdf_compress_error_compression_failed']; ?>');
                }
            },
            error: function() {
                showError('<?php echo $lang['pdf_compress_error_api_unavailable']; ?>');
            }
        });
    }
    
    function startStatusChecking() {
        statusCheckInterval = setInterval(function() {
            checkCompressionStatus();
        }, <?php echo $pdf_compress_auto_refresh_interval; ?>);
    }
    
    function checkCompressionStatus() {
        if (!currentJobId) return;
        
        $.ajax({
            url: 'ajax_status.php',
            type: 'GET',
            data: { job_id: currentJobId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    updateProgress(response.data);
                    
                    if (response.data.status === 'completed') {
                        showResults(response.data);
                        stopStatusChecking();
                    } else if (response.data.status === 'failed') {
                        showError(response.data.error_details || '<?php echo $lang['pdf_compress_error_compression_failed']; ?>');
                        stopStatusChecking();
                    }
                }
            },
            error: function() {
                // Continue checking - temporary network issues shouldn't stop the process
            }
        });
    }
    
    function updateProgress(data) {
        var progress = data.progress || 0;
        var message = data.message || '';
        
        $('#progress-fill').css('width', progress + '%');
        $('#progress-percentage').text(Math.round(progress) + '%');
        $('#progress-text').text(message);
        
        if (data.current_page && data.total_pages) {
            var details = '<?php echo $lang['pdf_compress_progress_processing']; ?>'
                .replace('{current}', data.current_page)
                .replace('{total}', data.total_pages);
            $('#progress-details').text(details);
        }
    }
    
    function showResults(data) {
        $('#compression-progress').hide();
        $('#compression-results').show();
        
        if (data.compressed_size_mb) {
            $('#compressed-size').text(formatFileSize(data.compressed_size_mb * 1024 * 1024));
        }
        
        if (data.compression_ratio) {
            $('#compression-ratio').text(data.compression_ratio.toFixed(1) + '%');
            
            var reduction = 100 - data.compression_ratio;
            $('#size-reduction').text(reduction.toFixed(1) + '%');
        }
        
        if (data.download_url) {
            $('#download-link').attr('href', data.download_url).show();
        }
        
        compressionInProgress = false;
    }
    
    function showError(message) {
        $('#compression-progress').hide();
        $('#compression-form').show();
        
        alert('<?php echo $lang['pdf_compress_error_title']; ?>: ' + message);
        compressionInProgress = false;
        stopStatusChecking();
    }
    
    function stopStatusChecking() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>

<?php
/**
 * Helper function to get PDF page count
 */
function pdf_compress_get_page_count($file_path) {
    // Try to get page count using various methods
    
    // Method 1: Use pdfinfo if available
    $output = shell_exec("pdfinfo \"$file_path\" 2>/dev/null | grep Pages");
    if ($output && preg_match('/Pages:\s*(\d+)/', $output, $matches)) {
        return (int)$matches[1];
    }
    
    // Method 2: Use PHP PDF libraries if available
    if (class_exists('setasign\Fpdi\Fpdi')) {
        try {
            $pdf = new setasign\Fpdi\Fpdi();
            return $pdf->setSourceFile($file_path);
        } catch (Exception $e) {
            // Continue to next method
        }
    }
    
    // Method 3: Simple regex search (less reliable)
    $content = file_get_contents($file_path);
    if ($content && preg_match_all('/\/Count\s+(\d+)/', $content, $matches)) {
        return max($matches[1]);
    }
    
    // Default fallback
    return 1;
}

include '../../../include/footer.php';
?>
