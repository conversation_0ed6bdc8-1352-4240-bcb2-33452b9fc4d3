<?php
/**
 * PDF Compression Application - Setup/Configuration Page
 * 
 * Admin configuration page for the PDF compression application
 */

include '../../../include/db.php';
include_once '../../../include/general.php';
include_once '../../../include/authenticate.php';

// Check if user is admin
if (!checkperm('a')) {
    exit($lang['error-permissiondenied']);
}

// Handle form submission
if (getval('save', '') !== '') {
    // API Settings
    $pdf_compress_api_url = getvalescaped('pdf_compress_api_url', '');
    $pdf_compress_api_key = getvalescaped('pdf_compress_api_key', '');
    
    // Application Settings
    $pdf_compress_max_file_size_mb = (int)getvalescaped('pdf_compress_max_file_size_mb', 300);
    $pdf_compress_show_in_main_menu = getvalescaped('pdf_compress_show_in_main_menu', '') === 'yes';
    $pdf_compress_menu_position = getvalescaped('pdf_compress_menu_position', 'tools');
    
    // Default Compression Settings
    $pdf_compress_default_quality = (int)getvalescaped('pdf_compress_default_quality', 75);
    $pdf_compress_default_scale = (float)getvalescaped('pdf_compress_default_scale', 0.8);
    $pdf_compress_default_method = getvalescaped('pdf_compress_default_method', 'auto');
    
    // UI Settings
    $pdf_compress_show_advanced_options = getvalescaped('pdf_compress_show_advanced_options', '') === 'yes';
    $pdf_compress_enable_progress_tracking = getvalescaped('pdf_compress_enable_progress_tracking', '') === 'yes';
    $pdf_compress_auto_refresh_interval = (int)getvalescaped('pdf_compress_auto_refresh_interval', 2000);
    
    // Access Control
    $pdf_compress_allowed_user_groups = getvalescaped('pdf_compress_allowed_user_groups', '');
    if (!empty($pdf_compress_allowed_user_groups)) {
        $pdf_compress_allowed_user_groups = explode(',', $pdf_compress_allowed_user_groups);
        $pdf_compress_allowed_user_groups = array_map('trim', $pdf_compress_allowed_user_groups);
        $pdf_compress_allowed_user_groups = array_filter($pdf_compress_allowed_user_groups);
    } else {
        $pdf_compress_allowed_user_groups = array();
    }
    
    // File Management
    $pdf_compress_cleanup_temp_files = getvalescaped('pdf_compress_cleanup_temp_files', '') === 'yes';
    $pdf_compress_temp_file_lifetime = (int)getvalescaped('pdf_compress_temp_file_lifetime', 3600);
    
    // Logging
    $pdf_compress_enable_logging = getvalescaped('pdf_compress_enable_logging', '') === 'yes';
    
    // Save configuration to database or config file
    // For this example, we'll save to the database using ResourceSpace's config system
    
    $config_updates = [
        'pdf_compress_api_url' => $pdf_compress_api_url,
        'pdf_compress_api_key' => $pdf_compress_api_key,
        'pdf_compress_max_file_size_mb' => $pdf_compress_max_file_size_mb,
        'pdf_compress_show_in_main_menu' => $pdf_compress_show_in_main_menu,
        'pdf_compress_menu_position' => $pdf_compress_menu_position,
        'pdf_compress_default_quality' => $pdf_compress_default_quality,
        'pdf_compress_default_scale' => $pdf_compress_default_scale,
        'pdf_compress_default_method' => $pdf_compress_default_method,
        'pdf_compress_show_advanced_options' => $pdf_compress_show_advanced_options,
        'pdf_compress_enable_progress_tracking' => $pdf_compress_enable_progress_tracking,
        'pdf_compress_auto_refresh_interval' => $pdf_compress_auto_refresh_interval,
        'pdf_compress_allowed_user_groups' => serialize($pdf_compress_allowed_user_groups),
        'pdf_compress_cleanup_temp_files' => $pdf_compress_cleanup_temp_files,
        'pdf_compress_temp_file_lifetime' => $pdf_compress_temp_file_lifetime,
        'pdf_compress_enable_logging' => $pdf_compress_enable_logging
    ];
    
    // Save each configuration value
    foreach ($config_updates as $key => $value) {
        set_config_option(null, $key, $value);
    }
    
    $message = $lang['pdf_compress_success_config_saved'];
    $message_type = 'success';
}

// Test API connection
if (getval('test_connection', '') !== '') {
    $test_result = pdf_compress_test_api_connection();
    if ($test_result['success']) {
        $message = $lang['pdf_compress_config_connection_success'];
        $message_type = 'success';
    } else {
        $message = $lang['pdf_compress_config_connection_failed'] . ': ' . $test_result['message'];
        $message_type = 'error';
    }
}

// Get current configuration values
$current_config = [
    'pdf_compress_api_url' => get_config_option(null, 'pdf_compress_api_url', $pdf_compress_api_url),
    'pdf_compress_api_key' => get_config_option(null, 'pdf_compress_api_key', $pdf_compress_api_key),
    'pdf_compress_max_file_size_mb' => get_config_option(null, 'pdf_compress_max_file_size_mb', $pdf_compress_max_file_size_mb),
    'pdf_compress_show_in_main_menu' => get_config_option(null, 'pdf_compress_show_in_main_menu', $pdf_compress_show_in_main_menu),
    'pdf_compress_menu_position' => get_config_option(null, 'pdf_compress_menu_position', $pdf_compress_menu_position),
    'pdf_compress_default_quality' => get_config_option(null, 'pdf_compress_default_quality', $pdf_compress_default_quality),
    'pdf_compress_default_scale' => get_config_option(null, 'pdf_compress_default_scale', $pdf_compress_default_scale),
    'pdf_compress_default_method' => get_config_option(null, 'pdf_compress_default_method', $pdf_compress_default_method),
    'pdf_compress_show_advanced_options' => get_config_option(null, 'pdf_compress_show_advanced_options', $pdf_compress_show_advanced_options),
    'pdf_compress_enable_progress_tracking' => get_config_option(null, 'pdf_compress_enable_progress_tracking', $pdf_compress_enable_progress_tracking),
    'pdf_compress_auto_refresh_interval' => get_config_option(null, 'pdf_compress_auto_refresh_interval', $pdf_compress_auto_refresh_interval),
    'pdf_compress_allowed_user_groups' => unserialize(get_config_option(null, 'pdf_compress_allowed_user_groups', serialize($pdf_compress_allowed_user_groups))),
    'pdf_compress_cleanup_temp_files' => get_config_option(null, 'pdf_compress_cleanup_temp_files', $pdf_compress_cleanup_temp_files),
    'pdf_compress_temp_file_lifetime' => get_config_option(null, 'pdf_compress_temp_file_lifetime', $pdf_compress_temp_file_lifetime),
    'pdf_compress_enable_logging' => get_config_option(null, 'pdf_compress_enable_logging', $pdf_compress_enable_logging)
];

// Get available user groups
$user_groups = get_usergroups();

// Include header
include '../../../include/header.php';
?>

<div class="BasicsBox">
    <h1>
        <i class="fa fa-cog fa-fw"></i>
        <?php echo $lang['pdf_compress_config_title']; ?>
    </h1>
    <p><?php echo $lang['pdf_compress_config_description']; ?></p>
</div>

<!-- Messages -->
<?php if (isset($message)): ?>
<div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible">
    <button type="button" class="close" data-dismiss="alert">&times;</button>
    <?php echo htmlspecialchars($message); ?>
</div>
<?php endif; ?>

<form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">

<!-- API Settings -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_config_api_settings']; ?></h2>
    
    <div class="Question">
        <label for="pdf_compress_api_url"><?php echo $lang['pdf_compress_config_api_url']; ?>:</label>
        <input type="url" name="pdf_compress_api_url" id="pdf_compress_api_url" 
               value="<?php echo htmlspecialchars($current_config['pdf_compress_api_url']); ?>" 
               class="stdwidth" required>
        <div class="clearerleft"></div>
        <div class="FormHelp">Enter the base URL of your PDF compression API service</div>
    </div>
    
    <div class="Question">
        <label for="pdf_compress_api_key"><?php echo $lang['pdf_compress_config_api_key']; ?>:</label>
        <input type="password" name="pdf_compress_api_key" id="pdf_compress_api_key" 
               value="<?php echo htmlspecialchars($current_config['pdf_compress_api_key']); ?>" 
               class="stdwidth" required>
        <div class="clearerleft"></div>
        <div class="FormHelp">API key for authentication with the compression service</div>
    </div>
    
    <div class="Question">
        <input type="submit" name="test_connection" value="<?php echo $lang['pdf_compress_config_test_connection']; ?>" 
               class="btn btn-secondary">
    </div>
</div>

<!-- Application Settings -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_config_app_settings']; ?></h2>
    
    <div class="Question">
        <label for="pdf_compress_max_file_size_mb"><?php echo $lang['pdf_compress_config_max_file_size']; ?>:</label>
        <input type="number" name="pdf_compress_max_file_size_mb" id="pdf_compress_max_file_size_mb" 
               value="<?php echo $current_config['pdf_compress_max_file_size_mb']; ?>" 
               min="1" max="1000" class="stdwidth" required>
        <div class="clearerleft"></div>
        <div class="FormHelp">Maximum file size allowed for compression (in MB)</div>
    </div>
    
    <div class="Question">
        <label>
            <input type="checkbox" name="pdf_compress_show_in_main_menu" value="yes" 
                   <?php echo $current_config['pdf_compress_show_in_main_menu'] ? 'checked' : ''; ?>>
            <?php echo $lang['pdf_compress_config_show_in_menu']; ?>
        </label>
        <div class="clearerleft"></div>
        <div class="FormHelp">Show PDF compression link in the main navigation menu</div>
    </div>
    
    <div class="Question">
        <label for="pdf_compress_menu_position"><?php echo $lang['pdf_compress_config_menu_position']; ?>:</label>
        <select name="pdf_compress_menu_position" id="pdf_compress_menu_position" class="stdwidth">
            <option value="main" <?php echo $current_config['pdf_compress_menu_position'] === 'main' ? 'selected' : ''; ?>>Main Menu</option>
            <option value="tools" <?php echo $current_config['pdf_compress_menu_position'] === 'tools' ? 'selected' : ''; ?>>Tools Menu</option>
            <option value="admin" <?php echo $current_config['pdf_compress_menu_position'] === 'admin' ? 'selected' : ''; ?>>Admin Menu</option>
        </select>
        <div class="clearerleft"></div>
        <div class="FormHelp">Where to place the PDF compression menu item</div>
    </div>
    
    <div class="Question">
        <label for="pdf_compress_allowed_user_groups"><?php echo $lang['pdf_compress_config_allowed_groups']; ?>:</label>
        <input type="text" name="pdf_compress_allowed_user_groups" id="pdf_compress_allowed_user_groups" 
               value="<?php echo implode(', ', $current_config['pdf_compress_allowed_user_groups']); ?>" 
               class="stdwidth" placeholder="Leave empty to allow all user groups">
        <div class="clearerleft"></div>
        <div class="FormHelp">Comma-separated list of user group IDs that can use the service (empty = all groups)</div>
        <div class="FormHelp">Available groups: 
            <?php 
            foreach ($user_groups as $group) {
                echo $group['ref'] . ' (' . htmlspecialchars($group['name']) . '), ';
            }
            ?>
        </div>
    </div>
</div>

<!-- Default Compression Settings -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_config_defaults']; ?></h2>
    
    <div class="Question">
        <label for="pdf_compress_default_quality"><?php echo $lang['pdf_compress_config_default_quality']; ?>:</label>
        <input type="range" name="pdf_compress_default_quality" id="pdf_compress_default_quality" 
               min="1" max="100" value="<?php echo $current_config['pdf_compress_default_quality']; ?>" 
               class="quality-slider">
        <span id="quality-display"><?php echo $current_config['pdf_compress_default_quality']; ?></span>
        <div class="clearerleft"></div>
        <div class="FormHelp">Default image quality setting (1-100)</div>
    </div>
    
    <div class="Question">
        <label for="pdf_compress_default_scale"><?php echo $lang['pdf_compress_config_default_scale']; ?>:</label>
        <input type="range" name="pdf_compress_default_scale" id="pdf_compress_default_scale" 
               min="0.1" max="1.0" step="0.1" value="<?php echo $current_config['pdf_compress_default_scale']; ?>" 
               class="scale-slider">
        <span id="scale-display"><?php echo $current_config['pdf_compress_default_scale']; ?></span>
        <div class="clearerleft"></div>
        <div class="FormHelp">Default image scale setting (0.1-1.0)</div>
    </div>
    
    <div class="Question">
        <label for="pdf_compress_default_method"><?php echo $lang['pdf_compress_config_default_method']; ?>:</label>
        <select name="pdf_compress_default_method" id="pdf_compress_default_method" class="stdwidth">
            <option value="auto" <?php echo $current_config['pdf_compress_default_method'] === 'auto' ? 'selected' : ''; ?>>Auto</option>
            <option value="pymupdf" <?php echo $current_config['pdf_compress_default_method'] === 'pymupdf' ? 'selected' : ''; ?>>PyMuPDF</option>
            <option value="ghostscript" <?php echo $current_config['pdf_compress_default_method'] === 'ghostscript' ? 'selected' : ''; ?>>Ghostscript</option>
            <option value="pdf2image" <?php echo $current_config['pdf_compress_default_method'] === 'pdf2image' ? 'selected' : ''; ?>>PDF2Image</option>
        </select>
        <div class="clearerleft"></div>
        <div class="FormHelp">Default compression method</div>
    </div>
</div>

<!-- UI Settings -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_config_ui']; ?></h2>
    
    <div class="Question">
        <label>
            <input type="checkbox" name="pdf_compress_show_advanced_options" value="yes" 
                   <?php echo $current_config['pdf_compress_show_advanced_options'] ? 'checked' : ''; ?>>
            <?php echo $lang['pdf_compress_config_show_advanced']; ?>
        </label>
        <div class="clearerleft"></div>
        <div class="FormHelp">Allow users to access advanced compression options</div>
    </div>
    
    <div class="Question">
        <label>
            <input type="checkbox" name="pdf_compress_enable_progress_tracking" value="yes" 
                   <?php echo $current_config['pdf_compress_enable_progress_tracking'] ? 'checked' : ''; ?>>
            <?php echo $lang['pdf_compress_config_enable_progress']; ?>
        </label>
        <div class="clearerleft"></div>
        <div class="FormHelp">Enable real-time progress tracking during compression</div>
    </div>
    
    <div class="Question">
        <label for="pdf_compress_auto_refresh_interval"><?php echo $lang['pdf_compress_config_refresh_interval']; ?>:</label>
        <input type="number" name="pdf_compress_auto_refresh_interval" id="pdf_compress_auto_refresh_interval" 
               value="<?php echo $current_config['pdf_compress_auto_refresh_interval']; ?>" 
               min="1000" max="10000" step="500" class="stdwidth">
        <div class="clearerleft"></div>
        <div class="FormHelp">How often to check compression progress (in milliseconds)</div>
    </div>
</div>

<!-- File Management -->
<div class="BasicsBox">
    <h2><?php echo $lang['pdf_compress_config_file_management']; ?></h2>
    
    <div class="Question">
        <label>
            <input type="checkbox" name="pdf_compress_cleanup_temp_files" value="yes" 
                   <?php echo $current_config['pdf_compress_cleanup_temp_files'] ? 'checked' : ''; ?>>
            <?php echo $lang['pdf_compress_config_cleanup_temp']; ?>
        </label>
        <div class="clearerleft"></div>
        <div class="FormHelp">Automatically clean up temporary files</div>
    </div>
    
    <div class="Question">
        <label for="pdf_compress_temp_file_lifetime"><?php echo $lang['pdf_compress_config_temp_lifetime']; ?>:</label>
        <input type="number" name="pdf_compress_temp_file_lifetime" id="pdf_compress_temp_file_lifetime" 
               value="<?php echo $current_config['pdf_compress_temp_file_lifetime']; ?>" 
               min="300" max="86400" class="stdwidth">
        <div class="clearerleft"></div>
        <div class="FormHelp">How long to keep temporary files (in seconds)</div>
    </div>
    
    <div class="Question">
        <label>
            <input type="checkbox" name="pdf_compress_enable_logging" value="yes" 
                   <?php echo $current_config['pdf_compress_enable_logging'] ? 'checked' : ''; ?>>
            <?php echo $lang['pdf_compress_enable_logging']; ?>
        </label>
        <div class="clearerleft"></div>
        <div class="FormHelp">Enable logging of compression activities</div>
    </div>
</div>

<div class="QuestionSubmit">
    <input type="submit" name="save" value="<?php echo $lang['save']; ?>" class="btn btn-primary">
    <input type="button" value="<?php echo $lang['cancel']; ?>" 
           onclick="CentralSpaceLoad('index.php', true);" class="btn">
</div>

</form>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Update slider displays
    $('#pdf_compress_default_quality').on('input', function() {
        $('#quality-display').text($(this).val());
    });
    
    $('#pdf_compress_default_scale').on('input', function() {
        $('#scale-display').text($(this).val());
    });
});
</script>

<?php
/**
 * Test API connection
 */
function pdf_compress_test_api_connection() {
    global $pdf_compress_api_url, $pdf_compress_api_key;
    
    $api_url = rtrim($pdf_compress_api_url, '/') . '/health';
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $api_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return ['success' => false, 'message' => $curl_error];
    }
    
    if ($http_code !== 200) {
        return ['success' => false, 'message' => "HTTP $http_code"];
    }
    
    $data = json_decode($response, true);
    if (!$data || !isset($data['status'])) {
        return ['success' => false, 'message' => 'Invalid response'];
    }
    
    if ($data['status'] !== 'healthy') {
        return ['success' => false, 'message' => 'Service not healthy'];
    }
    
    return ['success' => true, 'message' => 'Connection successful'];
}

// Add missing language strings
$lang['pdf_compress_config_description'] = 'Configure the PDF compression application settings';
$lang['pdf_compress_enable_logging'] = 'Enable Logging';
$lang['save'] = 'Save';
$lang['cancel'] = 'Cancel';

include '../../../include/footer.php';
?>
