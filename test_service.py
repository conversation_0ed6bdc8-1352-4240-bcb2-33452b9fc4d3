#!/usr/bin/env python3
"""
Test script for PDF Compression Service
"""

import requests
import time
import os
import sys
from pathlib import Path


def test_health_check(base_url):
    """Test the health check endpoint."""
    print("Testing health check...")
    
    try:
        response = requests.get(f"{base_url}/health")
        response.raise_for_status()
        
        health_data = response.json()
        print(f"✅ Health check passed: {health_data['status']}")
        print(f"   Version: {health_data['version']}")
        print(f"   Dependencies: {health_data['dependencies']}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False


def test_compression(base_url, pdf_file_path, api_key):
    """Test PDF compression workflow."""
    print(f"\nTesting compression with file: {pdf_file_path}")

    if not os.path.exists(pdf_file_path):
        print(f"❌ Test file not found: {pdf_file_path}")
        return False

    try:
        # Upload file for compression
        print("📤 Uploading file...")

        headers = {'Authorization': f'Bearer {api_key}'}

        with open(pdf_file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'image_quality': 70,
                'image_scale': 0.8,
                'skip_pages': '1',  # Skip first page
                'method': 'auto'
            }

            response = requests.post(f"{base_url}/compress", files=files, data=data, headers=headers)
            response.raise_for_status()
        
        upload_result = response.json()
        job_id = upload_result['job_id']
        print(f"✅ Upload successful, job ID: {job_id}")
        
        # Poll for status
        print("⏳ Waiting for compression to complete...")
        
        max_attempts = 60  # 60 seconds timeout
        attempt = 0
        
        while attempt < max_attempts:
            response = requests.get(f"{base_url}/status/{job_id}", headers=headers)
            response.raise_for_status()
            
            status_data = response.json()
            status = status_data['status']
            progress = status_data.get('progress', 0)
            message = status_data.get('message', '')
            
            print(f"   Status: {status} ({progress:.1f}%) - {message}")
            
            if status == 'completed':
                print("✅ Compression completed successfully!")
                
                # Print compression results
                original_size = status_data.get('original_size_mb', 0)
                compressed_size = status_data.get('compressed_size_mb', 0)
                compression_ratio = status_data.get('compression_ratio', 0)
                download_url = status_data.get('download_url')
                
                print(f"   Original size: {original_size:.2f} MB")
                print(f"   Compressed size: {compressed_size:.2f} MB")
                print(f"   Compression ratio: {compression_ratio:.1f}%")
                print(f"   Download URL: {download_url}")
                
                # Test download
                if download_url:
                    print("📥 Testing download...")
                    download_response = requests.get(download_url, headers=headers)
                    download_response.raise_for_status()
                    
                    if download_response.headers.get('content-type') == 'application/pdf':
                        print("✅ Download successful!")
                        
                        # Save downloaded file for inspection
                        output_path = "test_compressed_output.pdf"
                        with open(output_path, 'wb') as f:
                            f.write(download_response.content)
                        print(f"   Saved to: {output_path}")
                        
                        return True
                    else:
                        print("❌ Downloaded file is not a PDF")
                        return False
                
                return True
                
            elif status == 'failed':
                error_details = status_data.get('error_details', 'Unknown error')
                print(f"❌ Compression failed: {error_details}")
                return False
            
            elif status in ['pending', 'processing']:
                time.sleep(1)
                attempt += 1
            else:
                print(f"❌ Unknown status: {status}")
                return False
        
        print("❌ Timeout waiting for compression to complete")
        return False
        
    except Exception as e:
        print(f"❌ Compression test failed: {e}")
        return False


def test_invalid_file(base_url):
    """Test with invalid file."""
    print("\nTesting with invalid file...")
    
    try:
        # Create a fake PDF file
        fake_pdf_content = b"This is not a PDF file"
        
        files = {'file': ('fake.pdf', fake_pdf_content, 'application/pdf')}
        data = {'image_quality': 75}
        
        response = requests.post(f"{base_url}/compress", files=files, data=data)
        
        if response.status_code == 400:
            print("✅ Invalid file correctly rejected")
            return True
        else:
            print(f"❌ Expected 400 status code, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Invalid file test failed: {e}")
        return False


def test_nonexistent_job(base_url):
    """Test status check for nonexistent job."""
    print("\nTesting nonexistent job status...")
    
    try:
        fake_job_id = "00000000-0000-0000-0000-000000000000"
        response = requests.get(f"{base_url}/status/{fake_job_id}")
        
        if response.status_code == 404:
            print("✅ Nonexistent job correctly returns 404")
            return True
        else:
            print(f"❌ Expected 404 status code, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Nonexistent job test failed: {e}")
        return False


def main():
    """Run all tests."""
    base_url = os.getenv("BASE_URL", "http://localhost:8000")
    api_key = os.getenv("API_KEY", "pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1")

    print(f"🧪 Testing PDF Compression Service at {base_url}")
    print("=" * 60)
    
    # Test 1: Health check
    if not test_health_check(base_url):
        print("\n❌ Health check failed, aborting tests")
        sys.exit(1)
    
    # Test 2: Invalid file
    test_invalid_file(base_url)
    
    # Test 3: Nonexistent job
    test_nonexistent_job(base_url)
    
    # Test 4: Actual compression (if test file exists)
    test_files = [
        "input_2.pdf",  # Original test file
        "test.pdf",     # Generic test file
        "sample.pdf"    # Another common name
    ]
    
    test_file = None
    for file_path in test_files:
        if os.path.exists(file_path):
            test_file = file_path
            break
    
    if test_file:
        if test_compression(base_url, test_file, api_key):
            print("\n🎉 All tests passed!")
        else:
            print("\n❌ Compression test failed")
            sys.exit(1)
    else:
        print(f"\n⚠️  No test PDF file found. Tried: {', '.join(test_files)}")
        print("   Create a test PDF file to run compression tests")
    
    print("\n✅ Test suite completed")


if __name__ == "__main__":
    main()
