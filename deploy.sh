#!/bin/bash

# PDF Compression Service Deployment Script

set -e

echo "🚀 PDF Compression Service Deployment"
echo "======================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ Created .env file. Please review and update as needed."
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads outputs logs

# Set permissions
chmod 755 uploads outputs logs

# Build and start services
echo "🔨 Building Docker image..."
docker-compose build

echo "🚀 Starting services..."
docker-compose up -d

# Wait for service to be ready
echo "⏳ Waiting for service to be ready..."
sleep 10

# Check if service is running
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Service is running successfully!"
    echo ""
    echo "🌐 Service URLs:"
    echo "   API: http://localhost:8000"
    echo "   Health Check: http://localhost:8000/health"
    echo "   API Docs: http://localhost:8000/docs"
    echo ""
    echo "📊 Service Status:"
    docker-compose ps
    echo ""
    echo "📝 To view logs:"
    echo "   docker-compose logs -f"
    echo ""
    echo "🛑 To stop services:"
    echo "   docker-compose down"
else
    echo "❌ Service failed to start properly"
    echo "📋 Checking logs..."
    docker-compose logs
    exit 1
fi
