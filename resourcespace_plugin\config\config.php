<?php
/**
 * PDF Compress Plugin Configuration
 * 
 * Default configuration values for the PDF compression plugin
 */

// API Configuration
$pdf_compress_api_url = 'http://localhost:8000';
$pdf_compress_api_key = 'pdf_compress_2024_a7b9c3d5e8f1g4h6j9k2m5n8p1q4r7s0t3u6v9w2x5y8z1';

// Compression Settings
$pdf_compress_default_quality = 75;
$pdf_compress_default_scale = 0.8;
$pdf_compress_default_method = 'auto';
$pdf_compress_max_file_size_mb = 300;

// UI Settings
$pdf_compress_show_advanced_options = true;
$pdf_compress_enable_progress_tracking = true;
$pdf_compress_auto_refresh_interval = 2000; // milliseconds

// Resource Type Restrictions (empty array means all PDF resources)
$pdf_compress_allowed_resource_types = array();

// User Group Restrictions (empty array means all user groups can use)
$pdf_compress_allowed_user_groups = array();

// Timeout Settings
$pdf_compress_request_timeout = 300; // seconds
$pdf_compress_status_check_timeout = 30; // seconds

// File Management
$pdf_compress_cleanup_temp_files = true;
$pdf_compress_temp_file_lifetime = 3600; // seconds (1 hour)

// Logging
$pdf_compress_enable_logging = true;
$pdf_compress_log_level = 'info'; // debug, info, warning, error

// Alternative File Settings
$pdf_compress_create_alternative = true; // Create as alternative file instead of replacing
$pdf_compress_alternative_description = 'Compressed version';
